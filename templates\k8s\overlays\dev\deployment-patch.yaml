apiVersion: apps/v1
kind: Deployment
metadata:
  name: APP_NAME
spec:
  # Development rolling update strategy
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
      maxSurge: 50%
  progressDeadlineSeconds: 120
  revisionHistoryLimit: 3
  template:
    spec:
      # Development security context (relaxed)
      securityContext:
        runAsNonRoot: false
      containers:
      - name: APP_NAME
        securityContext:
          runAsNonRoot: false
          readOnlyRootFilesystem: false
          allowPrivilegeEscalation: true
        # Development health checks (faster)
        readinessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 3
          timeoutSeconds: 2
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        # Development resources (minimal)
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
