#!/usr/bin/env python3
"""
Test script for the dynamic multi-application connectivity system.
This script validates the new dynamic capabilities without requiring actual deployments.
"""

import sys
import os
import base64
import json
import tempfile
import shutil

# Add the scripts directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # Import the module using importlib to handle hyphens in filename
    import importlib.util
    spec = importlib.util.spec_from_file_location(
        "generate_manifests_cicd",
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "generate-manifests-cicd.py")
    )
    generate_manifests_cicd = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(generate_manifests_cicd)

    # Import the functions we need
    decode_secrets_payload = generate_manifests_cicd.decode_secrets_payload
    extract_application_config_from_secrets = generate_manifests_cicd.extract_application_config_from_secrets
    generate_dynamic_api_url = generate_manifests_cicd.generate_dynamic_api_url
    generate_backend_service_url = generate_manifests_cicd.generate_backend_service_url
    get_dynamic_backend_configuration = generate_manifests_cicd.get_dynamic_backend_configuration
    generate_dynamic_service_discovery_config = generate_manifests_cicd.generate_dynamic_service_discovery_config

    print("✅ Successfully imported dynamic system functions")
except Exception as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

def create_test_args(app_name, project_id, app_type, environment="staging"):
    """Create mock arguments object for testing"""
    class MockArgs:
        def __init__(self):
            self.app_name = app_name
            self.project_id = project_id
            self.application_type = app_type
            self.environment = environment
            self.docker_image = f"test/{project_id}"
            self.docker_tag = "v1.0.0"
            self.container_port = 8080
            self.health_check_path = "/health"
            self.enable_database = True
            self.backend_type = None
            self.backend_project_id = None
            self.backend_port = None
            self.source_repo = ""
            self.source_branch = ""
            self.commit_sha = ""
            self.secrets_encoded = None
    
    return MockArgs()

def test_secrets_payload_extraction():
    """Test extraction of configuration from secrets payload"""
    print("\n🧪 Testing secrets payload extraction...")
    
    # Create test secrets payload
    secrets_data = {
        "JWT_SECRET": "test-jwt-secret",
        "DB_PASSWORD": "test-db-password",
        "container_port": 3000,
        "health_check_path": "/api/health",
        "enable_database": False,
        "backend_type": "user-service",
        "backend_project_id": "user-api",
        "backend_port": 8080
    }
    
    # Encode as base64
    secrets_json = json.dumps(secrets_data)
    secrets_encoded = base64.b64encode(secrets_json.encode('utf-8')).decode('utf-8')
    
    # Test decoding
    decoded_secrets = decode_secrets_payload(secrets_encoded)
    
    assert decoded_secrets["container_port"] == 3000, "Container port extraction failed"
    assert decoded_secrets["health_check_path"] == "/api/health", "Health check path extraction failed"
    assert decoded_secrets["enable_database"] == False, "Database enable extraction failed"
    assert decoded_secrets["backend_project_id"] == "user-api", "Backend project ID extraction failed"
    
    print("✅ Secrets payload extraction working correctly")
    
    # Test configuration extraction
    args = create_test_args("Test App", "test-app", "web-app")
    updated_args = extract_application_config_from_secrets(decoded_secrets, args)
    
    assert updated_args.container_port == 3000, "Container port override failed"
    assert updated_args.health_check_path == "/api/health", "Health check path override failed"
    assert updated_args.enable_database == False, "Database enable override failed"
    assert updated_args.backend_project_id == "user-api", "Backend project ID assignment failed"
    
    print("✅ Configuration extraction from secrets working correctly")

def test_dynamic_backend_url_generation():
    """Test dynamic backend URL generation"""
    print("\n🧪 Testing dynamic backend URL generation...")
    
    # Test service URL generation
    backend_url = generate_backend_service_url("user-api", 8080, "staging")
    expected_url = "http://user-api-service.user-api-staging.svc.cluster.local:8080"
    
    assert backend_url == expected_url, f"Expected {expected_url}, got {backend_url}"
    print(f"✅ Backend URL generation: {backend_url}")
    
    # Test different environments
    dev_url = generate_backend_service_url("payment-service", 3000, "dev")
    prod_url = generate_backend_service_url("notification-api", 9000, "production")
    
    print(f"✅ Dev URL: {dev_url}")
    print(f"✅ Prod URL: {prod_url}")

def test_dynamic_backend_configuration():
    """Test dynamic backend configuration generation"""
    print("\n🧪 Testing dynamic backend configuration...")
    
    config = get_dynamic_backend_configuration(
        "user-auth", "user-api", 8080, "staging"
    )
    
    assert config["type"] == "user-auth", "Backend type mismatch"
    assert config["project_id"] == "user-api", "Project ID mismatch"
    assert config["port"] == 8080, "Port mismatch"
    assert config["namespace"] == "user-api-staging", "Namespace mismatch"
    assert "user-api-service" in config["url"], "Service name not in URL"
    
    print(f"✅ Backend configuration: {config}")

def test_frontend_backend_connectivity():
    """Test frontend to backend connectivity configuration"""
    print("\n🧪 Testing frontend-backend connectivity...")
    
    # Create frontend application args
    frontend_args = create_test_args("E-commerce Frontend", "ecommerce-frontend", "react-spa")
    frontend_args.backend_project_id = "user-api"
    frontend_args.backend_port = 8080
    frontend_args.backend_type = "user-auth"
    
    # Test API URL generation
    api_url = generate_dynamic_api_url(frontend_args)
    expected_url = "http://user-api-service.user-api-staging.svc.cluster.local:8080"
    
    assert api_url == expected_url, f"Expected {expected_url}, got {api_url}"
    print(f"✅ Frontend API URL: {api_url}")
    
    # Test service discovery configuration
    template_vars = {"TEST": "value"}
    service_discovery = generate_dynamic_service_discovery_config(frontend_args, template_vars)
    
    assert "current_service" in service_discovery, "Current service config missing"
    assert "backend_service" in service_discovery, "Backend service config missing"
    
    current = service_discovery["current_service"]
    backend = service_discovery["backend_service"]
    
    assert current["name"] == "ecommerce-frontend-service", "Current service name mismatch"
    assert backend["name"] == "user-api-service", "Backend service name mismatch"
    assert backend["url"] == expected_url, "Backend URL mismatch"
    
    print(f"✅ Service discovery config: {service_discovery}")

def test_unlimited_application_types():
    """Test support for unlimited application types"""
    print("\n🧪 Testing unlimited application types...")
    
    # Test various application types
    app_types = [
        "custom-microservice",
        "golang-api",
        "rust-service",
        "python-ml-service",
        "java-batch-processor",
        "nodejs-websocket-server",
        "php-legacy-app"
    ]
    
    for app_type in app_types:
        args = create_test_args(f"Test {app_type}", f"test-{app_type}", app_type)
        api_url = generate_dynamic_api_url(args)
        
        # Should generate self-service URL for backend applications
        expected_url = f"http://test-{app_type}-service:8080"
        assert expected_url in api_url, f"Self-service URL generation failed for {app_type}"
        
        print(f"✅ {app_type}: {api_url}")

def test_backward_compatibility():
    """Test backward compatibility with legacy system"""
    print("\n🧪 Testing backward compatibility...")
    
    # Test legacy backend types
    legacy_types = ["spring", "django", "nest"]
    
    for backend_type in legacy_types:
        args = create_test_args("Legacy Frontend", "legacy-frontend", "react-frontend")
        args.backend_type = backend_type
        
        api_url = generate_dynamic_api_url(args)
        
        # Should use legacy configuration
        assert "ai-" in api_url or "64.225.85.162" in api_url, f"Legacy compatibility failed for {backend_type}"
        
        print(f"✅ Legacy {backend_type}: {api_url}")

def run_all_tests():
    """Run all test functions"""
    print("🚀 Starting dynamic multi-application connectivity tests...\n")
    
    try:
        test_secrets_payload_extraction()
        test_dynamic_backend_url_generation()
        test_dynamic_backend_configuration()
        test_frontend_backend_connectivity()
        test_unlimited_application_types()
        test_backward_compatibility()
        
        print("\n🎉 All tests passed! The dynamic system is working correctly.")
        print("\n📋 Summary of validated capabilities:")
        print("   ✅ Secrets payload extraction and configuration override")
        print("   ✅ Dynamic backend URL generation for any application")
        print("   ✅ Unlimited application type support")
        print("   ✅ Frontend-backend connectivity configuration")
        print("   ✅ Service discovery configuration generation")
        print("   ✅ Backward compatibility with legacy system")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
