apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: react-frontend
    version: v1.0.0
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-react-frontend
      app.kubernetes.io/name: ai-react-frontend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-react-frontend
        app.kubernetes.io/name: ai-react-frontend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: react-frontend
        version: v1.0.0
        environment: dev
    spec:
      # React Frontend - No init containers needed (stateless)
      containers:
      - name: ai-react-frontend
        image: registry.digitalocean.com/doks-registry/ai-react-frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: ai-react-frontend-config
        # React Frontend - Volume mounts
        volumeMounts:
        # Mount env-config.js to the public folder
        - name: env-config-volume
          mountPath: /usr/share/nginx/html/env-config.js
          subPath: env-config.js
          readOnly: true
        # Mount nginx configuration
        - name: nginx-conf
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
        # Health Checks - Application Type Specific
        # TCP health checks for React Frontend to avoid application-specific failures
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      # Volumes for React Frontend
      volumes:
      # Volume for runtime environment configuration
      - name: env-config-volume
        configMap:
          name: ai-react-frontend-env
      # Volume for nginx configuration
      - name: nginx-conf
        configMap:
          name: ai-react-frontend-nginx-config
