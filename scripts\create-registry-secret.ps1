# PowerShell script to create DigitalOcean Container Registry authentication secret
# This script creates image pull secrets for accessing private DigitalOcean container registry

param(
    [string]$AccessToken = $env:DIGITALOCEAN_ACCESS_TOKEN,
    [string[]]$Namespaces = @(
        "ai-spring-backend-dev",
        "ai-spring-backend-staging", 
        "ai-spring-backend-production",
        "ai-nest-backend-dev",
        "ai-nest-backend-staging",
        "ai-nest-backend-production",
        "ai-react-frontend-dev",
        "ai-react-frontend-staging",
        "ai-react-frontend-production",
        "default"
    ),
    [string]$SecretName = "digitalocean-registry"
)

# Function to write colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check prerequisites
function Test-Prerequisites {
    Write-Status "Checking prerequisites..."
    
    # Check kubectl
    try {
        $null = kubectl version --client --output=json 2>$null
        Write-Success "kubectl is available"
    }
    catch {
        Write-Error "kubectl is not installed or not in PATH"
        exit 1
    }
    
    # Check doctl
    try {
        $null = doctl version 2>$null
        Write-Success "doctl is available"
    }
    catch {
        Write-Error "doctl is not installed or not in PATH"
        Write-Error "Please install doctl from: https://docs.digitalocean.com/reference/doctl/how-to/install/"
        exit 1
    }
}

# Check DigitalOcean authentication
function Test-DOAuthentication {
    Write-Status "Checking DigitalOcean authentication..."
    
    if ([string]::IsNullOrEmpty($AccessToken)) {
        Write-Error "DIGITALOCEAN_ACCESS_TOKEN is not provided"
        Write-Error "Please set it with: `$env:DIGITALOCEAN_ACCESS_TOKEN = 'your-token-here'"
        Write-Error "Or pass it as parameter: -AccessToken 'your-token-here'"
        exit 1
    }
    
    # Test doctl authentication
    try {
        doctl auth init --access-token $AccessToken 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Authentication failed"
        }
        Write-Success "DigitalOcean authentication successful"
    }
    catch {
        Write-Error "Failed to authenticate with DigitalOcean"
        Write-Error "Please check your DIGITALOCEAN_ACCESS_TOKEN"
        exit 1
    }
}

# Get registry credentials
function Get-RegistryCredentials {
    Write-Status "Getting DigitalOcean container registry credentials..."
    
    try {
        # Get registry login token
        $dockerConfig = doctl registry docker-config --expiry-seconds 3600 | ConvertFrom-Json
        $registryAuth = $dockerConfig.auths."registry.digitalocean.com".auth
        
        if ([string]::IsNullOrEmpty($registryAuth)) {
            throw "Registry token is empty"
        }
        
        # Decode the base64 token to get username:password
        $decodedAuth = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($registryAuth))
        $authParts = $decodedAuth.Split(':')
        
        if ($authParts.Length -ne 2) {
            throw "Invalid auth format"
        }
        
        Write-Success "Registry credentials obtained"
        return @{
            Username = $authParts[0]
            Password = $authParts[1]
        }
    }
    catch {
        Write-Error "Failed to get registry token from DigitalOcean: $_"
        exit 1
    }
}

# Create image pull secret in namespace
function New-ImagePullSecret {
    param(
        [string]$Namespace,
        [hashtable]$Credentials
    )
    
    Write-Status "Creating image pull secret in namespace: $Namespace"
    
    try {
        # Create namespace if it doesn't exist
        $namespaceExists = kubectl get namespace $Namespace 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Status "Creating namespace: $Namespace"
            kubectl create namespace $Namespace
            if ($LASTEXITCODE -ne 0) {
                throw "Failed to create namespace"
            }
        }
        
        # Delete existing secret if it exists
        $secretExists = kubectl get secret $SecretName -n $Namespace 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Warning "Deleting existing secret: $SecretName"
            kubectl delete secret $SecretName -n $Namespace
        }
        
        # Create the image pull secret
        kubectl create secret docker-registry $SecretName `
            --namespace=$Namespace `
            --docker-server=registry.digitalocean.com `
            --docker-username=$($Credentials.Username) `
            --docker-password=$($Credentials.Password) `
            --docker-email=unused
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Image pull secret created: $SecretName in namespace $Namespace"
            return $true
        }
        else {
            throw "kubectl create secret failed"
        }
    }
    catch {
        Write-Error "Failed to create image pull secret in namespace $Namespace : $_"
        return $false
    }
}

# Main execution
function Main {
    Write-Status "Starting DigitalOcean Container Registry secret creation..."
    
    # Check prerequisites
    Test-Prerequisites
    
    # Check DigitalOcean authentication
    Test-DOAuthentication
    
    # Get registry credentials
    $credentials = Get-RegistryCredentials
    
    # Create image pull secrets in all required namespaces
    $successCount = 0
    $totalCount = $Namespaces.Count
    
    foreach ($namespace in $Namespaces) {
        if (New-ImagePullSecret -Namespace $namespace -Credentials $credentials) {
            $successCount++
        }
    }
    
    if ($successCount -eq $totalCount) {
        Write-Success "All image pull secrets created successfully! ($successCount/$totalCount)"
    }
    else {
        Write-Warning "Some secrets failed to create. Success: $successCount/$totalCount"
    }
    
    Write-Status "You can now deploy applications that use DigitalOcean container registry"
    
    # Show created secrets
    Write-Status "Created secrets:"
    foreach ($namespace in $Namespaces) {
        $namespaceExists = kubectl get namespace $namespace 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  - $SecretName in $namespace" -ForegroundColor Cyan
        }
    }
}

# Run main function
Main
