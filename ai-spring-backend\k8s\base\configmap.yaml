apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-config
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "ai-spring-backend"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "8080"
  APP_NAME: "ai-spring-backend"
  APP_TYPE: "springboot-backend"

  # Database Configuration (DigitalOcean Managed PostgreSQL)
  DB_HOST: "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com"
  DB_PORT: "25060"
  DB_NAME: "spring_dev_db"
  DB_USER: "spring_dev_user"
  DB_SSL_MODE: "require"

  # SMTP Configuration
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_FROM: "<EMAIL>"

  # Dynamic Backend Configuration (for frontend applications)
  

  # Frontend-specific environment variables
  

  # Service Discovery Configuration
  SERVICE_NAME: "ai-spring-backend-service"
  SERVICE_NAMESPACE: "ai-spring-backend-dev"
  SERVICE_PORT: "8080"

  # Application-specific configurations will be added by overlays
