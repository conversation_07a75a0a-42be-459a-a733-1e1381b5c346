# Template Variables Reference

This document provides a comprehensive reference for all template variables available in the dynamic multi-stage rolling update deployment system.

## Core Application Variables

### Basic Application Information
- `{{PROJECT_ID}}` - Unique project identifier (lowercase alphanumeric with hyphens)
- `{{APP_NAME}}` - Human-readable application name
- `{{APP_TYPE}}` - Application type (react-frontend, springboot-backend, django-backend, nest-backend)
- `{{APP_VERSION}}` - Application version (default: "1.0.0")
- `{{ENVIRONMENT}}` - Target environment (dev, staging, production)
- `{{NAMESPACE}}` - Kubernetes namespace (format: {project-id}-{environment})

### Container Configuration
- `{{CONTAINER_IMAGE}}` - Full container image path with tag
- `{{DOCKER_IMAGE}}` - Docker image repository
- `{{DOCKER_TAG}}` - Docker image tag
- `{{CONTAINER_PORT}}` - Application port (3000 for React/NestJS, 8080 for Spring Boot, 8000 for Django)
- `{{IMAGE_PULL_POLICY}}` - Image pull policy (default: "Always")
- `{{IMAGE_PULL_SECRETS}}` - Image pull secrets configuration

## Rolling Update Strategy Variables

### Core Rolling Update Configuration
- `{{ROLLING_UPDATE_MAX_UNAVAILABLE}}` - Maximum unavailable pods during update
  - Dev: "50%", Staging: "25%", Production: "0%"
- `{{ROLLING_UPDATE_MAX_SURGE}}` - Maximum surge pods during update
  - Dev: "50%", Staging: "25%", Production: "33%"
- `{{ROLLING_UPDATE_PROGRESS_DEADLINE}}` - Progress deadline in seconds
  - Dev: "120", Staging: "300", Production: "600"
- `{{ROLLING_UPDATE_REVISION_HISTORY}}` - Number of old ReplicaSets to retain
  - Dev: "3", Staging: "5", Production: "10"

## Health Check Variables

### Startup Probe Configuration
- `{{STARTUP_PROBE_ENABLED}}` - Enable startup probe (boolean)
  - Dev: "false", Staging/Production: "true"
- `{{STARTUP_PROBE_INITIAL_DELAY}}` - Initial delay in seconds
  - Staging: "10", Production: "30"
- `{{STARTUP_PROBE_PERIOD}}` - Period between probes in seconds
  - Staging: "5", Production: "10"
- `{{STARTUP_PROBE_TIMEOUT}}` - Timeout for each probe in seconds
  - Staging: "3", Production: "5"
- `{{STARTUP_PROBE_FAILURE_THRESHOLD}}` - Number of failures before giving up
  - Staging: "5", Production: "10"
- `{{STARTUP_PROBE_SUCCESS_THRESHOLD}}` - Number of successes to mark as ready (default: "1")

### Readiness Probe Configuration
- `{{READINESS_PROBE_INITIAL_DELAY}}` - Initial delay in seconds
  - Dev: "5", Staging: "10", Production: "15"
- `{{READINESS_PROBE_PERIOD}}` - Period between probes in seconds
  - Dev: "3", Staging/Production: "5"/"10"
- `{{READINESS_PROBE_TIMEOUT}}` - Timeout for each probe in seconds
  - Dev: "2", Staging: "3", Production: "5"
- `{{READINESS_PROBE_FAILURE_THRESHOLD}}` - Number of failures before marking unready
  - All environments: "3"
- `{{READINESS_PROBE_SUCCESS_THRESHOLD}}` - Number of successes to mark as ready (default: "1")

### Liveness Probe Configuration
- `{{LIVENESS_PROBE_INITIAL_DELAY}}` - Initial delay in seconds
  - Dev: "15", Staging: "30", Production: "60"
- `{{LIVENESS_PROBE_PERIOD}}` - Period between probes in seconds
  - Dev: "10", Staging: "10", Production: "30"
- `{{LIVENESS_PROBE_TIMEOUT}}` - Timeout for each probe in seconds
  - Dev: "5", Staging: "5", Production: "10"
- `{{LIVENESS_PROBE_FAILURE_THRESHOLD}}` - Number of failures before restarting
  - All environments: "3"
- `{{LIVENESS_PROBE_SUCCESS_THRESHOLD}}` - Number of successes to mark as healthy (default: "1")

### Health Check Endpoint Configuration
- `{{HEALTH_CHECK_PATH}}` - HTTP health check path
  - React Frontend: "/health"
  - Spring Boot: "/actuator/health"
  - Django: "/health/"
  - NestJS: "/health"
- `{{HEALTH_CHECK_PORT}}` - Health check port (matches CONTAINER_PORT)
- `{{HEALTH_CHECK_SCHEME}}` - HTTP scheme (default: "HTTP")
- `{{HEALTH_CHECK_HEADERS}}` - Custom HTTP headers for health checks

## Resource Management Variables

### Replica Configuration
- `{{REPLICAS_DEFAULT}}` - Default number of replicas
  - Dev: "2", Staging: "3", Production: "3"
- `{{REPLICAS_MIN}}` - Minimum number of replicas
  - Dev: "2", Staging: "3", Production: "3"
- `{{REPLICAS_MAX}}` - Maximum number of replicas for HPA
  - Dev: "4", Staging: "6", Production: "10"

### CPU and Memory Resources
- `{{CPU_REQUEST}}` - CPU resource request
  - Dev: "200m", Staging: "300m", Production: "500m"
- `{{CPU_LIMIT}}` - CPU resource limit
  - Dev: "500m", Staging: "800m", Production: "1000m"
- `{{MEMORY_REQUEST}}` - Memory resource request
  - Dev: "256Mi", Staging: "512Mi", Production: "1Gi"
- `{{MEMORY_LIMIT}}` - Memory resource limit
  - Dev: "512Mi", Staging: "1Gi", Production: "2Gi"

### Storage Resources
- `{{EPHEMERAL_STORAGE_REQUEST}}` - Ephemeral storage request
- `{{EPHEMERAL_STORAGE_LIMIT}}` - Ephemeral storage limit

## Security Context Variables

### Pod Security Context
- `{{SECURITY_RUN_AS_NON_ROOT}}` - Run as non-root user (boolean)
  - Dev: "false", Staging/Production: "true"
- `{{SECURITY_RUN_AS_USER}}` - User ID to run as
  - Staging/Production: "1000"
- `{{SECURITY_RUN_AS_GROUP}}` - Group ID to run as
  - Staging/Production: "1000"
- `{{SECURITY_FS_GROUP}}` - File system group ID
  - Production: "1000"
- `{{SECURITY_SUPPLEMENTAL_GROUPS}}` - Supplemental groups

### Container Security Context
- `{{SECURITY_READ_ONLY_ROOT_FS}}` - Read-only root filesystem (boolean)
  - Dev: "false", Staging/Production: "true"
- `{{SECURITY_ALLOW_PRIVILEGE_ESCALATION}}` - Allow privilege escalation (boolean)
  - Dev: "true", Staging/Production: "false"
- `{{SECURITY_CAPABILITIES_DROP}}` - Capabilities to drop
  - Production: "ALL"
- `{{SECURITY_CAPABILITIES_ADD}}` - Capabilities to add

## Pod Disruption Budget Variables

- `{{PDB_ENABLED}}` - Enable Pod Disruption Budget (boolean, default: "true")
- `{{PDB_MIN_AVAILABLE}}` - Minimum available pods
- `{{PDB_MAX_UNAVAILABLE}}` - Maximum unavailable pods
  - Dev: "50%", Staging: "25%", Production: "1"
- `{{PDB_UNHEALTHY_POD_EVICTION_POLICY}}` - Unhealthy pod eviction policy
- `{{PDB_ANNOTATIONS}}` - Custom annotations for PDB

## Horizontal Pod Autoscaler Variables

### HPA Configuration
- `{{HPA_ENABLED}}` - Enable HPA (boolean)
  - Dev: "false", Staging/Production: "true"
- `{{HPA_MIN_REPLICAS}}` - HPA minimum replicas
- `{{HPA_MAX_REPLICAS}}` - HPA maximum replicas
- `{{HPA_TARGET_CPU_UTILIZATION}}` - Target CPU utilization percentage (default: "70")
- `{{HPA_TARGET_MEMORY_UTILIZATION}}` - Target memory utilization percentage (default: "80")

### HPA Scaling Behavior
- `{{HPA_SCALE_DOWN_STABILIZATION}}` - Scale down stabilization window (default: "300")
- `{{HPA_SCALE_UP_STABILIZATION}}` - Scale up stabilization window (default: "60")
- `{{HPA_SCALE_DOWN_PERCENT}}` - Scale down percentage
  - Dev: "10", Staging: "10", Production: "5"
- `{{HPA_SCALE_UP_PERCENT}}` - Scale up percentage
  - Dev: "50", Staging: "25", Production: "10"
- `{{HPA_SCALE_DOWN_PODS}}` - Scale down pods count (default: "1")
- `{{HPA_SCALE_UP_PODS}}` - Scale up pods count
  - Dev: "2", Staging/Production: "1"
- `{{HPA_SCALE_DOWN_PERIOD}}` - Scale down period in seconds
  - Dev/Staging: "60", Production: "120"
- `{{HPA_SCALE_UP_PERIOD}}` - Scale up period in seconds
  - Dev: "30", Staging: "30", Production: "60"

### Custom Metrics
- `{{HPA_CUSTOM_METRICS}}` - Custom metrics configuration

## Resource Quota Variables

### Quota Configuration
- `{{RESOURCE_QUOTA_ENABLED}}` - Enable resource quota (boolean, default: "true")
- `{{RESOURCE_QUOTA_CPU_REQUESTS}}` - Total CPU requests limit
- `{{RESOURCE_QUOTA_CPU_LIMITS}}` - Total CPU limits
- `{{RESOURCE_QUOTA_MEMORY_REQUESTS}}` - Total memory requests limit
- `{{RESOURCE_QUOTA_MEMORY_LIMITS}}` - Total memory limits
- `{{RESOURCE_QUOTA_STORAGE_REQUESTS}}` - Total storage requests limit
- `{{RESOURCE_QUOTA_EPHEMERAL_STORAGE}}` - Ephemeral storage quota

### Object Count Quotas
- `{{RESOURCE_QUOTA_PODS}}` - Maximum number of pods
- `{{RESOURCE_QUOTA_SERVICES}}` - Maximum number of services
- `{{RESOURCE_QUOTA_SECRETS}}` - Maximum number of secrets
- `{{RESOURCE_QUOTA_CONFIGMAPS}}` - Maximum number of configmaps
- `{{RESOURCE_QUOTA_PVC}}` - Maximum number of PVCs
- `{{RESOURCE_QUOTA_LOAD_BALANCERS}}` - Maximum number of load balancers
- `{{RESOURCE_QUOTA_NODE_PORTS}}` - Maximum number of node ports

## Limit Range Variables

### Container Limits
- `{{LIMIT_RANGE_ENABLED}}` - Enable limit range (boolean, default: "true")
- `{{LIMIT_RANGE_CONTAINER_CPU_DEFAULT}}` - Default CPU limit for containers
- `{{LIMIT_RANGE_CONTAINER_MEMORY_DEFAULT}}` - Default memory limit for containers
- `{{LIMIT_RANGE_CONTAINER_CPU_REQUEST}}` - Default CPU request for containers
- `{{LIMIT_RANGE_CONTAINER_MEMORY_REQUEST}}` - Default memory request for containers
- `{{LIMIT_RANGE_CONTAINER_CPU_MAX}}` - Maximum CPU limit for containers
- `{{LIMIT_RANGE_CONTAINER_MEMORY_MAX}}` - Maximum memory limit for containers
- `{{LIMIT_RANGE_CONTAINER_CPU_MIN}}` - Minimum CPU request for containers
- `{{LIMIT_RANGE_CONTAINER_MEMORY_MIN}}` - Minimum memory request for containers

### Pod and PVC Limits
- `{{LIMIT_RANGE_POD_CPU_MAX}}` - Maximum CPU for pods
- `{{LIMIT_RANGE_POD_MEMORY_MAX}}` - Maximum memory for pods
- `{{LIMIT_RANGE_PVC_ENABLED}}` - Enable PVC limits (boolean)
- `{{LIMIT_RANGE_PVC_STORAGE_MAX}}` - Maximum PVC storage
- `{{LIMIT_RANGE_PVC_STORAGE_MIN}}` - Minimum PVC storage

## Network Policy Variables

### Network Policy Configuration
- `{{NETWORK_POLICY_ENABLED}}` - Enable network policy (boolean)
  - Dev: "false", Staging/Production: "true"
- `{{NETWORK_POLICY_INGRESS_ENABLED}}` - Enable ingress rules (boolean)
- `{{NETWORK_POLICY_EGRESS_ENABLED}}` - Enable egress rules (boolean)
- `{{NETWORK_POLICY_INGRESS_RULES}}` - Custom ingress rules
- `{{NETWORK_POLICY_EGRESS_RULES}}` - Custom egress rules
- `{{NETWORK_POLICY_ALLOW_HTTP_EGRESS}}` - Allow HTTP egress (boolean)

## Service Configuration Variables

### Service Configuration
- `{{SERVICE_TYPE}}` - Service type (default: "LoadBalancer")
- `{{SERVICE_CLUSTER_IP}}` - Cluster IP for service
- `{{SERVICE_EXTERNAL_IPS}}` - External IPs for service
- `{{SERVICE_LOAD_BALANCER_IP}}` - Load balancer IP
- `{{SERVICE_LOAD_BALANCER_SOURCE_RANGES}}` - Load balancer source ranges
- `{{SERVICE_EXTERNAL_TRAFFIC_POLICY}}` - External traffic policy
- `{{SERVICE_SESSION_AFFINITY}}` - Session affinity configuration
- `{{SERVICE_SESSION_AFFINITY_CONFIG}}` - Session affinity config details
- `{{NODE_PORT}}` - Node port for NodePort services
- `{{ADDITIONAL_SERVICE_PORTS}}` - Additional service ports

## Database Configuration Variables

### Database Settings
- `{{ENABLE_DATABASE}}` - Enable database configuration (boolean, no local deployment)
- `{{DB_HOST}}` - Database host
  - All environments: "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com"
- `{{DB_PORT}}` - Database port (default: "25060")
- `{{DB_NAME}}` - Database name (environment-specific: spring_dev_db, {project-id}_staging_db, {project-id}_production_db)
- `{{DB_USER}}` - Database username (default: "spring_dev_user")
- `{{DB_PASSWORD}}` - Database password (DigitalOcean managed: AVNS_0bYzt0GZdky7rnP8Kl7)

## Authentication and Secrets Variables

### JWT Configuration
- `{{JWT_SECRET}}` - JWT signing secret (from secrets)
- `{{JWT_SECRET_B64}}` - Base64 encoded JWT secret

### SMTP Configuration
- `{{SMTP_HOST}}` - SMTP server host
- `{{SMTP_PORT}}` - SMTP server port (default: "587")
- `{{SMTP_FROM}}` - SMTP from address
- `{{SMTP_USER}}` - SMTP username (from secrets)
- `{{SMTP_PASS}}` - SMTP password (from secrets)
- `{{SMTP_USER_B64}}` - Base64 encoded SMTP user
- `{{SMTP_PASS_B64}}` - Base64 encoded SMTP password

### OAuth Configuration
- `{{GOOGLE_CLIENT_ID}}` - Google OAuth client ID (from secrets)
- `{{GOOGLE_CLIENT_SECRET}}` - Google OAuth client secret (from secrets)
- `{{GOOGLE_CLIENT_ID_B64}}` - Base64 encoded Google client ID
- `{{GOOGLE_CLIENT_SECRET_B64}}` - Base64 encoded Google client secret

## Cluster and Infrastructure Variables

### Cluster Configuration
- `{{CLUSTER_SERVER}}` - Kubernetes cluster server URL
- `{{CLUSTER_NAME}}` - Cluster name
- `{{CLUSTER_ID}}` - Cluster identifier

### Monitoring and Observability
- `{{PROMETHEUS_SCRAPE_ENABLED}}` - Enable Prometheus scraping (boolean, default: "true")
- `{{PROMETHEUS_SCRAPE_PORT}}` - Prometheus scrape port (default: "8080")
- `{{PROMETHEUS_SCRAPE_PATH}}` - Prometheus scrape path (default: "/metrics")

## Advanced Configuration Variables

### Pod Configuration
- `{{SERVICE_ACCOUNT_NAME}}` - Service account name
- `{{NODE_SELECTOR}}` - Node selector configuration
- `{{TOLERATIONS}}` - Pod tolerations
- `{{AFFINITY}}` - Pod affinity rules
- `{{TERMINATION_GRACE_PERIOD}}` - Termination grace period (default: "30")

### Volume Configuration
- `{{VOLUMES}}` - Custom volumes configuration
- `{{VOLUME_MOUNTS}}` - Custom volume mounts
- `{{LIFECYCLE_HOOKS}}` - Container lifecycle hooks

### Annotations
- `{{DEPLOYMENT_ANNOTATIONS}}` - Custom deployment annotations
- `{{POD_ANNOTATIONS}}` - Custom pod annotations
- `{{SERVICE_ANNOTATIONS}}` - Custom service annotations

## Environment-Specific Defaults

The system automatically applies environment-specific defaults for all variables based on the target environment (dev, staging, production). These defaults ensure optimal configuration for each deployment stage while maintaining consistency and best practices.

All variables can be overridden through the CI/CD payload or GitHub issue form, providing flexibility while maintaining sensible defaults for rapid deployment.
