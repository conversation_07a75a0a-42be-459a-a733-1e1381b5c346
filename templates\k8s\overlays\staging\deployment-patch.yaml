apiVersion: apps/v1
kind: Deployment
metadata:
  name: APP_NAME
spec:
  # Staging rolling update strategy
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  progressDeadlineSeconds: 300
  revisionHistoryLimit: 5
  template:
    spec:
      # Staging security context (enhanced)
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
      containers:
      - name: APP_NAME
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          readOnlyRootFilesystem: true
          allowPrivilegeEscalation: false
        # Staging health checks (with startup probe)
        startupProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 5
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        # Staging resources (moderate)
        resources:
          requests:
            memory: "512Mi"
            cpu: "300m"
          limits:
            memory: "1Gi"
            cpu: "800m"
