apiVersion: v1
kind: LimitRange
metadata:
  name: APP_NAME-limit-range
  namespace: NAMESPACE
  labels:
    app: APP_NAME
    app.kubernetes.io/name: APP_NAME
    app.kubernetes.io/component: resource-management
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: APP_NAME
    environment: ENVIRONMENT
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  limits:
  # Container Limits
  - type: Container
    default:
      cpu: "LIMIT_RANGE_CONTAINER_CPU_DEFAULT"
      memory: "LIMIT_RANGE_CONTAINER_MEMORY_DEFAULT"
      ephemeral-storage: "LIMIT_RANGE_CONTAINER_EPHEMERAL_STORAGE_DEFAULT"
    defaultRequest:
      cpu: "LIMIT_RANGE_CONTAINER_CPU_REQUEST"
      memory: "LIMIT_RANGE_CONTAINER_MEMORY_REQUEST"
      ephemeral-storage: "LIMIT_RANGE_CONTAINER_EPHEMERAL_STORAGE_REQUEST"
    max:
      cpu: "LIMIT_RANGE_CONTAINER_CPU_MAX"
      memory: "LIMIT_RANGE_CONTAINER_MEMORY_MAX"
      ephemeral-storage: "LIMIT_RANGE_CONTAINER_EPHEMERAL_STORAGE_MAX"
    min:
      cpu: "LIMIT_RANGE_CONTAINER_CPU_MIN"
      memory: "LIMIT_RANGE_CONTAINER_MEMORY_MIN"
      ephemeral-storage: "LIMIT_RANGE_CONTAINER_EPHEMERAL_STORAGE_MIN"
  
  # Pod Limits
  - type: Pod
    max:
      cpu: "LIMIT_RANGE_POD_CPU_MAX"
      memory: "LIMIT_RANGE_POD_MEMORY_MAX"
  
  # PVC Limits
  - type: PersistentVolumeClaim
    max:
      storage: "LIMIT_RANGE_PVC_STORAGE_MAX"
    min:
      storage: "LIMIT_RANGE_PVC_STORAGE_MIN"
