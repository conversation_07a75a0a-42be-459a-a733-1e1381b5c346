apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: ai-spring-backend-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-spring-backend-project
    environment: dev
spec:
  description: "ai-spring-backend Project for GitOps deployment"
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  destinations:
  - namespace: ai-spring-backend-dev
    server: https://kubernetes.default.svc
  - namespace: argocd
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: storage.k8s.io
    kind: StorageClass
  namespaceResourceWhitelist:
  - group: ''
    kind: ConfigMap
  - group: ''
    kind: Secret
  - group: ''
    kind: Service
  - group: ''
    kind: ServiceAccount
  - group: apps
    kind: Deployment
  - group: apps
    kind: ReplicaSet
  - group: ''
    kind: Pod
  - group: networking.k8s.io
    kind: Ingress
  - group: networking.k8s.io
    kind: NetworkPolicy
  - group: autoscaling
    kind: HorizontalPodAutoscaler
  - group: policy
    kind: PodDisruptionBudget
  - group: ''
    kind: PersistentVolumeClaim
  roles:
  - name: admin
    description: Admin access to ai-spring-backend project
    policies:
    - p, proj:ai-spring-backend-project:admin, applications, *, ai-spring-backend-project/*, allow
    - p, proj:ai-spring-backend-project:admin, repositories, *, *, allow
    groups:
    - argocd-admins