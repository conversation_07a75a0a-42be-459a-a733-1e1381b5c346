apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: APP_NAME-network-policy
  namespace: NAMESPACE
  labels:
    app: APP_NAME
    app.kubernetes.io/name: APP_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: APP_NAME
    environment: ENVIRONMENT
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  podSelector:
    matchLabels:
      app: APP_NAME
      app.kubernetes.io/name: APP_NAME
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from ingress controllers and load balancers
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: CONTAINER_PORT

  # Allow health check probes from kube-system
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: CONTAINER_PORT

  # Allow ingress from other application namespaces (for inter-app connectivity)
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: Exists
    ports:
    - protocol: TCP
      port: CONTAINER_PORT

  # Allow ingress from frontend applications to backend services
  - from:
    - podSelector:
        matchExpressions:
        - key: app.kubernetes.io/component
          operator: In
          values:
          - react-frontend
          - vue-frontend
          - angular-frontend
          - web-app
          - frontend
    ports:
    - protocol: TCP
      port: CONTAINER_PORT
  
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS for external API calls
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow communication within the same namespace
  - to:
    - namespaceSelector:
        matchLabels:
          name: NAMESPACE

  # Allow communication to other application namespaces (for inter-app connectivity)
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: In
          values:
          - NAMESPACE
        - key: name
          operator: Exists
    ports:
    - protocol: TCP
      port: 8080  # Spring Boot
    - protocol: TCP
      port: 3000  # React/NestJS
    - protocol: TCP
      port: 8000  # Django
    - protocol: TCP
      port: 5000  # Flask
    - protocol: TCP
      port: 4000  # GraphQL
    - protocol: TCP
      port: 9000  # Custom apps

  # Allow communication to kube-system for service discovery
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 443

  # Allow HTTP communication for inter-application calls
  - to: []
    ports:
    - protocol: TCP
      port: 80
