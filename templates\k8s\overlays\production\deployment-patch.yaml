apiVersion: apps/v1
kind: Deployment
metadata:
  name: APP_NAME
spec:
  # Production rolling update strategy (zero-downtime)
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0%
      maxSurge: 33%
  progressDeadlineSeconds: 600
  revisionHistoryLimit: 10
  template:
    spec:
      # Production security context (maximum security)
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: APP_NAME
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          readOnlyRootFilesystem: true
          allowPrivilegeEscalation: false
          capabilities:
            drop: ["ALL"]
        # Production health checks (comprehensive)
        startupProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: CONTAINER_PORT
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        # Production resources (high performance)
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
