# PowerShell script to verify DigitalOcean Container Registry authentication setup
# This script checks if image pull secrets are properly configured

param(
    [string[]]$Namespaces = @(
        "ai-spring-backend-dev",
        "ai-nest-backend-dev", 
        "ai-react-frontend-dev"
    ),
    [string]$SecretName = "digitalocean-registry"
)

# Function to write colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if kubectl is available
function Test-Kubectl {
    try {
        $null = kubectl version --client --output=json 2>$null
        Write-Success "kubectl is available"
        return $true
    }
    catch {
        Write-Error "kubectl is not available"
        return $false
    }
}

# Check if namespace exists
function Test-Namespace {
    param([string]$Namespace)
    
    try {
        $null = kubectl get namespace $Namespace 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Namespace exists: $Namespace"
            return $true
        }
        else {
            Write-Warning "Namespace does not exist: $Namespace"
            return $false
        }
    }
    catch {
        Write-Warning "Failed to check namespace: $Namespace"
        return $false
    }
}

# Check if registry secret exists
function Test-RegistrySecret {
    param([string]$Namespace)
    
    try {
        $secret = kubectl get secret $SecretName -n $Namespace -o json 2>$null | ConvertFrom-Json
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Registry secret exists: $SecretName in $Namespace"
            
            # Check if it's a docker-registry type secret
            if ($secret.type -eq "kubernetes.io/dockerconfigjson") {
                Write-Success "Secret type is correct: kubernetes.io/dockerconfigjson"
                return $true
            }
            else {
                Write-Warning "Secret type is incorrect: $($secret.type)"
                return $false
            }
        }
        else {
            Write-Error "Registry secret does not exist: $SecretName in $Namespace"
            return $false
        }
    }
    catch {
        Write-Error "Failed to check registry secret in namespace: $Namespace"
        return $false
    }
}

# Check deployment configuration
function Test-DeploymentConfig {
    param([string]$Namespace, [string]$AppName)
    
    try {
        $deployments = kubectl get deployments -n $Namespace -o json 2>$null | ConvertFrom-Json
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "No deployments found in namespace: $Namespace"
            return $false
        }
        
        $found = $false
        foreach ($deployment in $deployments.items) {
            if ($deployment.metadata.name -like "*$AppName*") {
                $found = $true
                $imagePullSecrets = $deployment.spec.template.spec.imagePullSecrets
                
                if ($imagePullSecrets -and ($imagePullSecrets | Where-Object { $_.name -eq $SecretName })) {
                    Write-Success "Deployment $($deployment.metadata.name) has imagePullSecrets configured"
                }
                else {
                    Write-Warning "Deployment $($deployment.metadata.name) missing imagePullSecrets"
                }
            }
        }
        
        if (-not $found) {
            Write-Warning "No deployments found for app: $AppName in namespace: $Namespace"
        }
        
        return $found
    }
    catch {
        Write-Error "Failed to check deployments in namespace: $Namespace"
        return $false
    }
}

# Test image pull capability
function Test-ImagePull {
    param([string]$Namespace, [string]$Image)
    
    Write-Status "Testing image pull capability for: $Image"
    
    try {
        # Create a test pod to verify image pull
        $testPodName = "registry-test-$(Get-Random -Minimum 1000 -Maximum 9999)"
        
        $testPodYaml = @"
apiVersion: v1
kind: Pod
metadata:
  name: $testPodName
  namespace: $Namespace
spec:
  imagePullSecrets:
  - name: $SecretName
  containers:
  - name: test
    image: $Image
    command: ['sleep', '10']
  restartPolicy: Never
"@
        
        # Apply the test pod
        $testPodYaml | kubectl apply -f - 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Test pod created, waiting for image pull..."
            
            # Wait for pod to start or fail
            Start-Sleep -Seconds 15
            
            $podStatus = kubectl get pod $testPodName -n $Namespace -o jsonpath='{.status.phase}' 2>$null
            
            if ($podStatus -eq "Running" -or $podStatus -eq "Succeeded") {
                Write-Success "Image pull successful for: $Image"
                $result = $true
            }
            else {
                Write-Error "Image pull failed for: $Image (Pod status: $podStatus)"
                # Show pod events for debugging
                kubectl describe pod $testPodName -n $Namespace 2>$null | Select-String -Pattern "Events:" -A 10
                $result = $false
            }
            
            # Clean up test pod
            kubectl delete pod $testPodName -n $Namespace 2>$null
        }
        else {
            Write-Error "Failed to create test pod"
            $result = $false
        }
        
        return $result
    }
    catch {
        Write-Error "Failed to test image pull: $_"
        return $false
    }
}

# Main verification function
function Start-Verification {
    Write-Status "Starting DigitalOcean Container Registry authentication verification..."
    
    # Check prerequisites
    if (-not (Test-Kubectl)) {
        Write-Error "kubectl is required but not available"
        return
    }
    
    $overallSuccess = $true
    
    # Check each namespace
    foreach ($namespace in $Namespaces) {
        Write-Status "Checking namespace: $namespace"
        
        $namespaceExists = Test-Namespace -Namespace $namespace
        $secretExists = $false
        $deploymentConfigured = $false
        
        if ($namespaceExists) {
            $secretExists = Test-RegistrySecret -Namespace $namespace
            
            # Check deployment configuration based on namespace
            $appName = switch -Wildcard ($namespace) {
                "*spring*" { "spring" }
                "*nest*" { "nest" }
                "*react*" { "react" }
                default { "app" }
            }
            
            $deploymentConfigured = Test-DeploymentConfig -Namespace $namespace -AppName $appName
        }
        
        # Summary for this namespace
        if ($namespaceExists -and $secretExists) {
            Write-Success "✅ Namespace $namespace is properly configured"
        }
        else {
            Write-Warning "⚠️  Namespace $namespace has issues"
            $overallSuccess = $false
        }
        
        Write-Host "" # Empty line for readability
    }
    
    # Overall summary
    Write-Host "=" * 60 -ForegroundColor Cyan
    if ($overallSuccess) {
        Write-Success "🎉 All registry authentication checks passed!"
        Write-Status "Your applications should now be able to pull images from DigitalOcean registry"
    }
    else {
        Write-Warning "⚠️  Some issues were found. Please review the output above."
        Write-Status "Run the registry secret creation script to fix missing secrets:"
        Write-Host "    .\scripts\create-registry-secret.ps1" -ForegroundColor Yellow
    }
    Write-Host "=" * 60 -ForegroundColor Cyan
}

# Run the verification
Start-Verification
