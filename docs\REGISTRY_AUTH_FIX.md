# DigitalOcean Container Registry Authentication Fix

## Problem Description

You're experiencing `ImagePullBackOff` errors with `401 Unauthorized` responses when Kubernetes tries to pull images from DigitalOcean's private container registry (`registry.digitalocean.com/doks-registry/`).

**Root Cause**: Your Kubernetes cluster is not authenticated to pull images from the private DigitalOcean container registry.

## Solution Overview

The fix involves three main steps:
1. Create image pull secrets for DigitalOcean container registry authentication
2. Update deployment manifests to use these secrets
3. Ensure the GitOps automation includes registry authentication

## Step 1: Create Registry Authentication Secrets

### Option A: Using the Automated Script (Recommended)

1. **Set your DigitalOcean access token:**
   ```powershell
   $env:DIGITALOCEAN_ACCESS_TOKEN = "your-digitalocean-token-here"
   ```

2. **Run the registry secret creation script:**
   ```powershell
   .\scripts\create-registry-secret.ps1
   ```

### Option B: Manual Creation

If you prefer to create the secrets manually:

1. **Get your DigitalOcean registry token:**
   ```powershell
   doctl auth init --access-token "your-digitalocean-token-here"
   $registryToken = doctl registry docker-config --expiry-seconds 3600 | ConvertFrom-Json
   $authString = $registryToken.auths."registry.digitalocean.com".auth
   $decodedAuth = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($authString))
   $username, $password = $decodedAuth.Split(':')
   ```

2. **Create the image pull secret for each namespace:**
   ```powershell
   # For ai-spring-backend-dev namespace
   kubectl create namespace ai-spring-backend-dev --dry-run=client -o yaml | kubectl apply -f -
   kubectl create secret docker-registry digitalocean-registry `
     --namespace=ai-spring-backend-dev `
     --docker-server=registry.digitalocean.com `
     --docker-username=$username `
     --docker-password=$password `
     --docker-email=unused
   
   # Repeat for other namespaces as needed
   ```

## Step 2: Verify Deployment Updates

The deployment manifests have been updated to include `imagePullSecrets`. Verify these changes:

### Updated Files:
- ✅ `templates/k8s/base/deployment.yaml` - Template updated
- ✅ `ai-spring-backend/k8s/base/deployment.yaml` - Updated
- ✅ `ai-nest-backend/k8s/base/deployment.yaml` - Updated  
- ✅ `ai-react-frontend/k8s/deployment.yaml` - Updated

### Example of the added configuration:
```yaml
spec:
  # Image pull secrets for private container registries
  imagePullSecrets:
  - name: digitalocean-registry
  securityContext:
    runAsNonRoot: false
  # ... rest of spec
```

## Step 3: Test the Fix

1. **Verify secrets are created:**
   ```powershell
   kubectl get secrets -n ai-spring-backend-dev | Select-String "digitalocean-registry"
   kubectl get secrets -n ai-nest-backend-dev | Select-String "digitalocean-registry"
   kubectl get secrets -n ai-react-frontend-dev | Select-String "digitalocean-registry"
   ```

2. **Test deployment:**
   ```powershell
   # Apply a deployment to test
   kubectl apply -f ai-spring-backend/k8s/base/
   
   # Check pod status
   kubectl get pods -n ai-spring-backend-dev
   
   # Check events for any remaining issues
   kubectl get events -n ai-spring-backend-dev --sort-by='.lastTimestamp'
   ```

## Step 4: Update GitOps Automation (Optional)

To ensure future deployments automatically have registry access, you can update the GitOps workflow to create registry secrets automatically.

### Add to `.github/workflows/deploy-from-cicd.yaml`:

```yaml
- name: 🔐 Create Registry Secrets
  if: steps.setup-kubectl.outputs.kubectl-available == 'true'
  run: |
    echo "🔐 Creating DigitalOcean registry secrets..."
    
    # Set the access token
    export DIGITALOCEAN_ACCESS_TOKEN="${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}"
    
    # Run the registry secret creation script
    chmod +x scripts/create-registry-secret.sh
    ./scripts/create-registry-secret.sh
```

## Troubleshooting

### Issue: "Failed to get registry token"
**Solution**: Ensure your DigitalOcean access token has the correct permissions:
- Go to [DigitalOcean API Tokens](https://cloud.digitalocean.com/account/api/tokens)
- Verify the token has **Read + Write** permissions
- Make sure the token is not expired

### Issue: "Secret already exists"
**Solution**: The script automatically deletes and recreates existing secrets. If you see this error:
```powershell
kubectl delete secret digitalocean-registry -n <namespace>
```

### Issue: "Namespace not found"
**Solution**: The script creates namespaces automatically. If manual creation is needed:
```powershell
kubectl create namespace <namespace-name>
```

### Issue: Still getting ImagePullBackOff
**Checklist**:
1. ✅ Registry secret exists in the correct namespace
2. ✅ Deployment has `imagePullSecrets` configured
3. ✅ Image name and tag are correct
4. ✅ DigitalOcean token has registry access permissions

## Security Notes

- Registry tokens expire after 1 hour by default
- For production, consider using longer-lived tokens or automated renewal
- Store the DigitalOcean access token securely in GitHub Secrets
- Regularly rotate access tokens for security

## Next Steps

After fixing the registry authentication:
1. Test deploying applications through the GitOps workflow
2. Monitor ArgoCD for successful synchronization
3. Verify applications start successfully without ImagePullBackOff errors
4. Consider setting up automated token renewal for production environments
