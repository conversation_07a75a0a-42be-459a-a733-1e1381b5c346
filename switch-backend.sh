#!/bin/bash

# Industry Standard Backend Switching Script
# Usage: ./switch-backend.sh [spring|nest|django]

set -e

BACKEND_TYPE=$1
NAMESPACE="ai-react-frontend-dev"
CONFIGMAP_NAME="ai-react-frontend-runtime-config"

if [ -z "$BACKEND_TYPE" ]; then
    echo "❌ Error: Backend type required"
    echo "Usage: $0 [spring|nest|django]"
    exit 1
fi

echo "🔄 Switching to $BACKEND_TYPE backend..."

# Define backend configurations
case $BACKEND_TYPE in
    "spring")
        BACKEND_URL="http://*************:8080"
        SERVICE_NAME="ai-spring-backend-service"
        SERVICE_NAMESPACE="ai-spring-backend-dev"
        ;;
    "nest")
        BACKEND_URL="http://************:3000"
        SERVICE_NAME="ai-nest-backend-service"
        SERVICE_NAMESPACE="ai-nest-backend-dev"
        ;;
    "django")
        BACKEND_URL="http://*************:8000"
        SERVICE_NAME="ai-django-backend-service"
        SERVICE_NAMESPACE="ai-django-backend-dev"
        ;;
    *)
        echo "❌ Error: Invalid backend type '$BACKEND_TYPE'"
        echo "Supported backends: spring, nest, django"
        exit 1
        ;;
esac

# Create new configuration
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
NEW_CONFIG=$(cat <<EOF
{
  "currentBackend": "$BACKEND_TYPE",
  "backendUrl": "$BACKEND_URL",
  "environment": "dev",
  "serviceName": "$SERVICE_NAME",
  "namespace": "$SERVICE_NAMESPACE",
  "apiVersion": "v1",
  "lastUpdated": "$TIMESTAMP",
  "supportedBackends": [
    {
      "name": "spring",
      "url": "http://*************:8080",
      "internalUrl": "http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-dev",
      "status": "ready",
      "healthEndpoint": "/actuator/health"
    },
    {
      "name": "django",
      "url": "http://*************:8000",
      "internalUrl": "http://ai-django-backend-service.ai-django-backend-dev.svc.cluster.local:8000",
      "serviceName": "ai-django-backend-service",
      "namespace": "ai-django-backend-dev",
      "status": "ready",
      "healthEndpoint": "/health"
    },
    {
      "name": "nest",
      "url": "http://************:3000",
      "internalUrl": "http://ai-nest-backend-service.ai-nest-backend-dev.svc.cluster.local:3000",
      "serviceName": "ai-nest-backend-service",
      "namespace": "ai-nest-backend-dev",
      "status": "ready",
      "healthEndpoint": "/health"
    }
  ]
}
EOF
)

# Update ConfigMap
echo "📝 Updating ConfigMap..."
kubectl patch configmap $CONFIGMAP_NAME -n $NAMESPACE --type merge -p "{\"data\":{\"runtime-config.json\":\"$(echo "$NEW_CONFIG" | sed 's/"/\\"/g' | tr -d '\n')\"}}"

# Restart frontend deployment to pick up changes
echo "🔄 Restarting frontend deployment..."
kubectl rollout restart deployment ai-react-frontend -n $NAMESPACE

# Wait for rollout to complete
echo "⏳ Waiting for deployment to complete..."
kubectl rollout status deployment ai-react-frontend -n $NAMESPACE --timeout=300s

# Verify the switch
echo "🧪 Verifying backend switch..."
sleep 5
CURRENT_CONFIG=$(kubectl exec deployment/ai-react-frontend -n $NAMESPACE -- curl -s http://localhost:3000/api/config 2>/dev/null || echo "Failed to get config")

if echo "$CURRENT_CONFIG" | grep -q "\"currentBackend\":\"$BACKEND_TYPE\""; then
    echo "✅ Successfully switched to $BACKEND_TYPE backend!"
    echo "🎯 Backend URL: $BACKEND_URL"
    echo "📊 Service: $SERVICE_NAME in $SERVICE_NAMESPACE"
else
    echo "❌ Backend switch verification failed"
    echo "Current config: $CURRENT_CONFIG"
    exit 1
fi

echo "🎉 Backend switch completed successfully!"
