apiVersion: v1
kind: ResourceQuota
metadata:
  name: APP_NAME-resource-quota
spec:
  hard:
    # Production resource quotas (high capacity)
    requests.cpu: "3500m"
    limits.cpu: "8000m"
    requests.memory: "7Gi"
    limits.memory: "16Gi"
    requests.storage: "100Gi"
    pods: "20"
    services: "5"
    secrets: "10"
    configmaps: "10"
    persistentvolumeclaims: "5"
    services.loadbalancers: "2"
    services.nodeports: "3"
