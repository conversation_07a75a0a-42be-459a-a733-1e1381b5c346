# Sample Payloads for Kustomize-Based GitOps System

This document provides comprehensive sample payloads for the Kustomize-based GitOps automation system with dynamic multi-stage rolling update deployment capabilities.

## React Frontend Application (No Database)

### Repository Dispatch Payload

```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "My React Dashboard",
    "project_id": "my-react-dashboard",
    "application_type": "react-frontend",
    "environment": "dev",
    "docker_image": "myorg/react-dashboard",
    "docker_tag": "v1.2.0",
    "container_port": 3000,
    "source_repo": "myorg/react-dashboard",
    "source_branch": "main",
    "commit_sha": "abc123def456",
    "enable_database": false,
    "health_check_path": "/health",
    "secrets_encoded": "eyJHT09HTEVfQ0xJRU5UX0lEIjoiMTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3VpbG9yb3IzZm0xanFjNDkzb3MuYXBwcy5nb29nbGV1c2VyY29udGVudC5jb20iLCJHT09HTEVfQ0xJRU5UX1NFQ1JFVCI6IkdPQ1NQWC03MkY0TjRIOWhpTElZNVN6NWdzQnMyOThBQWJUIiwiSldUX1NFQ1JFVCI6InN1cGVyc2VjcmV0a2V5In0="
  }
}
```

### Decoded Secrets (Base64)
```json
{
  "GOOGLE_CLIENT_ID": "1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com",
  "GOOGLE_CLIENT_SECRET": "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",
  "JWT_SECRET": "supersecretkey"
}
```

### Generated Configuration Variables

The system will automatically generate the following configuration based on the payload:

#### Environment-Specific Rolling Update Strategy (Dev)
```yaml
ROLLING_UPDATE_MAX_UNAVAILABLE: "50%"
ROLLING_UPDATE_MAX_SURGE: "50%"
ROLLING_UPDATE_PROGRESS_DEADLINE: "120"
ROLLING_UPDATE_REVISION_HISTORY: "3"
```

#### Health Check Configuration (Dev)
```yaml
STARTUP_PROBE_ENABLED: "false"
READINESS_PROBE_INITIAL_DELAY: "5"
READINESS_PROBE_PERIOD: "3"
READINESS_PROBE_TIMEOUT: "2"
READINESS_PROBE_FAILURE_THRESHOLD: "3"
LIVENESS_PROBE_INITIAL_DELAY: "15"
LIVENESS_PROBE_PERIOD: "10"
LIVENESS_PROBE_TIMEOUT: "5"
LIVENESS_PROBE_FAILURE_THRESHOLD: "3"
HEALTH_CHECK_PATH: "/health"
HEALTH_CHECK_PORT: "3000"
HEALTH_CHECK_SCHEME: "HTTP"
```

#### Resource Configuration (Dev)
```yaml
REPLICAS_DEFAULT: "2"
REPLICAS_MIN: "2"
REPLICAS_MAX: "4"
CPU_REQUEST: "200m"
CPU_LIMIT: "500m"
MEMORY_REQUEST: "256Mi"
MEMORY_LIMIT: "512Mi"
```

#### Security Context (Dev)
```yaml
SECURITY_RUN_AS_NON_ROOT: "false"
SECURITY_READ_ONLY_ROOT_FS: "false"
SECURITY_ALLOW_PRIVILEGE_ESCALATION: "true"
```

#### Pod Disruption Budget
```yaml
PDB_ENABLED: "true"
PDB_MAX_UNAVAILABLE: "50%"
```

#### HPA Configuration (Disabled in Dev)
```yaml
HPA_ENABLED: "false"
HPA_MIN_REPLICAS: "2"
HPA_MAX_REPLICAS: "4"
HPA_TARGET_CPU_UTILIZATION: "70"
HPA_TARGET_MEMORY_UTILIZATION: "80"
```

## Spring Boot Backend Application (With Database)

### Repository Dispatch Payload

```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "User Authentication API",
    "project_id": "user-auth-api",
    "application_type": "springboot-backend",
    "environment": "staging",
    "docker_image": "myorg/user-auth-api",
    "docker_tag": "v2.1.0",
    "container_port": 8080,
    "source_repo": "myorg/user-auth-api",
    "source_branch": "main",
    "commit_sha": "def456ghi789",
    "enable_database": true,
    "health_check_path": "/actuator/health",
    "secrets_encoded": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  }
}
```

### Environment-Specific Configuration (Staging)

#### Rolling Update Strategy
```yaml
ROLLING_UPDATE_MAX_UNAVAILABLE: "25%"
ROLLING_UPDATE_MAX_SURGE: "25%"
ROLLING_UPDATE_PROGRESS_DEADLINE: "300"
ROLLING_UPDATE_REVISION_HISTORY: "5"
```

#### Health Check Configuration
```yaml
STARTUP_PROBE_ENABLED: "true"
STARTUP_PROBE_INITIAL_DELAY: "10"
STARTUP_PROBE_PERIOD: "5"
STARTUP_PROBE_TIMEOUT: "3"
STARTUP_PROBE_FAILURE_THRESHOLD: "5"
READINESS_PROBE_INITIAL_DELAY: "10"
READINESS_PROBE_PERIOD: "5"
READINESS_PROBE_TIMEOUT: "3"
READINESS_PROBE_FAILURE_THRESHOLD: "3"
LIVENESS_PROBE_INITIAL_DELAY: "30"
LIVENESS_PROBE_PERIOD: "10"
LIVENESS_PROBE_TIMEOUT: "5"
LIVENESS_PROBE_FAILURE_THRESHOLD: "3"
HEALTH_CHECK_PATH: "/actuator/health"
HEALTH_CHECK_PORT: "8080"
```

#### Resource Configuration
```yaml
REPLICAS_DEFAULT: "3"
REPLICAS_MIN: "3"
REPLICAS_MAX: "6"
CPU_REQUEST: "300m"
CPU_LIMIT: "800m"
MEMORY_REQUEST: "512Mi"
MEMORY_LIMIT: "1Gi"
```

#### Security Context
```yaml
SECURITY_RUN_AS_NON_ROOT: "true"
SECURITY_RUN_AS_USER: "1000"
SECURITY_RUN_AS_GROUP: "1000"
SECURITY_READ_ONLY_ROOT_FS: "true"
SECURITY_ALLOW_PRIVILEGE_ESCALATION: "false"
```

#### HPA Configuration (Enabled in Staging)
```yaml
HPA_ENABLED: "true"
HPA_MIN_REPLICAS: "3"
HPA_MAX_REPLICAS: "6"
HPA_TARGET_CPU_UTILIZATION: "70"
HPA_TARGET_MEMORY_UTILIZATION: "80"
HPA_SCALE_DOWN_STABILIZATION: "300"
HPA_SCALE_UP_STABILIZATION: "60"
```

## Production Environment Configuration

### Sample Production Payload (NestJS Backend)

```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "Payment Processing Service",
    "project_id": "payment-service",
    "application_type": "nest-backend",
    "environment": "production",
    "docker_image": "myorg/payment-service",
    "docker_tag": "v3.0.1",
    "container_port": 3000,
    "source_repo": "myorg/payment-service",
    "source_branch": "main",
    "commit_sha": "ghi789jkl012",
    "enable_database": true,
    "health_check_path": "/health",
    "secrets_encoded": "************************************************************************************************************************************************************************************************************************************************************************************************************"
  }
}
```

### Production Configuration Highlights

#### Zero-Downtime Rolling Update
```yaml
ROLLING_UPDATE_MAX_UNAVAILABLE: "0%"
ROLLING_UPDATE_MAX_SURGE: "33%"
ROLLING_UPDATE_PROGRESS_DEADLINE: "600"
ROLLING_UPDATE_REVISION_HISTORY: "10"
```

#### Enhanced Security
```yaml
SECURITY_RUN_AS_NON_ROOT: "true"
SECURITY_RUN_AS_USER: "1000"
SECURITY_RUN_AS_GROUP: "1000"
SECURITY_FS_GROUP: "1000"
SECURITY_READ_ONLY_ROOT_FS: "true"
SECURITY_ALLOW_PRIVILEGE_ESCALATION: "false"
SECURITY_CAPABILITIES_DROP: "ALL"
```

#### High Availability
```yaml
REPLICAS_DEFAULT: "3"
REPLICAS_MIN: "3"
REPLICAS_MAX: "10"
PDB_ENABLED: "true"
PDB_MAX_UNAVAILABLE: "1"
HPA_ENABLED: "true"
```

#### Resource Optimization
```yaml
CPU_REQUEST: "500m"
CPU_LIMIT: "1000m"
MEMORY_REQUEST: "1Gi"
MEMORY_LIMIT: "2Gi"
```

## Cluster Targeting

### Development/Staging Cluster
```yaml
CLUSTER_SERVER: "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com"
CLUSTER_NAME: "dev-staging-cluster"
CLUSTER_ID: "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
```

### Production Cluster
```yaml
CLUSTER_SERVER: "https://e9d23ae8-213c-4746-b379-330f85c0a0cf.k8s.ondigitalocean.com"
CLUSTER_NAME: "production-cluster"
CLUSTER_ID: "e9d23ae8-213c-4746-b379-330f85c0a0cf"
```

## Generated Kubernetes Resources

For each deployment, the system generates:

1. **Core Resources**:
   - Namespace
   - Deployment (with rolling update strategy)
   - Service (LoadBalancer type)
   - ConfigMap (application configuration)
   - Secret (base64 encoded sensitive data)

2. **Enhanced Resources**:
   - PodDisruptionBudget (availability protection)
   - HorizontalPodAutoscaler (auto-scaling)
   - ResourceQuota (namespace limits)
   - LimitRange (default resource constraints)
   - NetworkPolicy (security isolation)

3. **ArgoCD Resources**:
   - Application (with sync policies)
   - AppProject (with RBAC)

4. **Database Resources** (if enabled):
   - PostgreSQL Deployment
   - PostgreSQL Service
   - PostgreSQL PVC

## Usage Examples

### Triggering from CI/CD Pipeline

```bash
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "My React App",
      "project_id": "my-react-app",
      "application_type": "react-frontend",
      "environment": "dev",
      "docker_image": "myorg/react-app",
      "docker_tag": "latest",
      "container_port": 3000,
      "source_repo": "myorg/react-app",
      "source_branch": "main",
      "commit_sha": "abc123",
      "enable_database": false,
      "health_check_path": "/health",
      "secrets_encoded": "base64-encoded-secrets"
    }
  }'
```

### Testing Locally

```bash
python scripts/generate-manifests-cicd.py \
  --app-name "Test React App" \
  --project-id "test-react-app" \
  --application-type "react-frontend" \
  --environment "dev" \
  --docker-image "nginx" \
  --docker-tag "alpine" \
  --container-port 3000 \
  --source-repo "test/react-app" \
  --source-branch "main" \
  --commit-sha "test123" \
  --output-dir "."
```

## Kustomize-Based Deployment

The system now supports Kustomize-based deployments as an alternative to template-based generation. This provides better GitOps practices with declarative configuration management.

### Kustomize Directory Structure

When using Kustomize mode, the system generates:

```
project-id/
├── argocd/
│   └── application.yaml          # ArgoCD Application pointing to Kustomize overlay
└── k8s/
    └── overlays/
        └── environment/          # dev, staging, or production
            ├── kustomization.yaml
            ├── deployment-patch.yaml
            ├── resource-patch.yaml
            ├── security-patch.yaml
            ├── hpa-patch.yaml    # staging/production only
            └── pdb-patch.yaml    # staging/production only
```

### Using Kustomize Mode

#### Via CI/CD Pipeline

Add `"use_kustomize": true` to your repository dispatch payload:

```bash
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "My Spring Boot API",
      "project_id": "my-spring-api",
      "application_type": "springboot-backend",
      "environment": "production",
      "docker_image": "myorg/spring-api",
      "docker_tag": "v1.0.0",
      "container_port": 8080,
      "enable_database": true,
      "use_kustomize": true,
      "secrets_encoded": "base64-encoded-secrets"
    }
  }'
```

#### Via Command Line

```bash
python scripts/generate-manifests-cicd.py \
  --use-kustomize \
  --app-name "My Spring Boot API" \
  --project-id "my-spring-api" \
  --application-type "springboot-backend" \
  --environment "production" \
  --docker-image "myorg/spring-api" \
  --docker-tag "v1.0.0" \
  --container-port 8080 \
  --enable-database
```

### Kustomize Benefits

1. **Declarative Configuration**: Environment-specific patches are clearly defined
2. **GitOps Best Practices**: Configuration drift detection and automatic reconciliation
3. **Reusable Base**: Common configuration shared across environments
4. **Environment Isolation**: Clear separation of environment-specific settings
5. **Validation**: Built-in YAML validation and resource verification

### Building and Applying Kustomize Configurations

#### Build Environment-Specific Manifests

```bash
# Development environment
kustomize build project-id/k8s/overlays/dev

# Staging environment
kustomize build project-id/k8s/overlays/staging

# Production environment
kustomize build project-id/k8s/overlays/production
```

#### Apply to Cluster

```bash
# Apply development configuration
kustomize build project-id/k8s/overlays/dev | kubectl apply -f -

# Apply staging configuration
kustomize build project-id/k8s/overlays/staging | kubectl apply -f -

# Apply production configuration
kustomize build project-id/k8s/overlays/production | kubectl apply -f -
```

### Kustomize Benefits

The Kustomize-based deployment system provides:

1. **Declarative Configuration**: Environment-specific patches are clearly defined
2. **GitOps Best Practices**: Configuration drift detection and automatic reconciliation
3. **Reusable Base**: Common configuration shared across environments
4. **Environment Isolation**: Clear separation of environment-specific settings
5. **Validation**: Built-in YAML validation and resource verification
6. **Industry Standard**: Kubernetes-native configuration management

This enhanced system provides complete automation with industry-standard deployment practices, ensuring reliable, secure, and scalable application deployments across all environments.
