apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: base-application
  annotations:
    config.kubernetes.io/local-config: "true"

# Base resources that will be customized by overlays
resources:
- namespace.yaml
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

# Common labels applied to all resources
commonLabels:
  app.kubernetes.io/managed-by: argocd

# Common annotations applied to all resources
commonAnnotations:
  argocd.argoproj.io/tracking-id: "APP_NAME:ai-spring-backend-dev"

# Name prefix for all resources (will be overridden by overlays)
namePrefix: ""

# Namespace for all resources (will be overridden by overlays)
namespace: ai-spring-backend-dev

# Images that can be customized by overlays
images:
- name: registry.digitalocean.com/doks-registry/ai-spring-backend:latest
  newTag: DOCKER_TAG

# ConfigMap and Secret generators can be added by overlays
configMapGenerator: []
secretGenerator: []

# Patches that can be applied by overlays
patches: []

# Replacements for placeholder values (handled by overlays)
# replacements: []
