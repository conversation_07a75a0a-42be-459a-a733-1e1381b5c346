# Kustomize Templates for GitOps Deployment System

This directory contains the Kustomize-based deployment templates that serve as the single deployment method for the GitOps automation system.

## Directory Structure

```
templates/k8s/
├── base/                    # Base Kubernetes manifests
│   ├── deployment.yaml      # Base deployment configuration
│   ├── service.yaml         # Base service configuration
│   ├── configmap.yaml       # Base configmap configuration
│   ├── secret.yaml          # Base secret configuration
│   ├── namespace.yaml       # Base namespace configuration
│   ├── pdb.yaml            # Pod Disruption Budget
│   ├── hpa.yaml            # Horizontal Pod Autoscaler
│   ├── resource-quota.yaml  # Resource Quota
│   ├── limit-range.yaml    # Limit Range
│   ├── network-policy.yaml # Network Policy
│   └── kustomization.yaml  # Base kustomization
└── overlays/               # Environment-specific overlays
    ├── dev/                # Development environment
    │   ├── kustomization.yaml
    │   ├── deployment-patch.yaml
    │   ├── resource-patch.yaml
    │   ├── security-patch.yaml
    │   ├── disable-hpa-patch.yaml
    │   └── disable-network-policy-patch.yaml
    ├── staging/            # Staging environment
    │   ├── kustomization.yaml
    │   ├── deployment-patch.yaml
    │   ├── resource-patch.yaml
    │   ├── security-patch.yaml
    │   ├── hpa-patch.yaml
    │   └── pdb-patch.yaml
    └── production/         # Production environment
        ├── kustomization.yaml
        ├── deployment-patch.yaml
        ├── resource-patch.yaml
        ├── security-patch.yaml
        ├── hpa-patch.yaml
        └── pdb-patch.yaml
```

## Environment-Specific Configurations

### Development
- Rolling Update: maxUnavailable: 50%, maxSurge: 50%
- Progress Deadline: 120 seconds
- Replicas: 2
- Resources: CPU 200m-500m, Memory 256Mi-512Mi
- Security: Relaxed for development
- HPA: Disabled
- Network Policy: Disabled

### Staging
- Rolling Update: maxUnavailable: 25%, maxSurge: 25%
- Progress Deadline: 300 seconds
- Replicas: 3
- Resources: CPU 300m-800m, Memory 512Mi-1Gi
- Security: Enhanced with non-root users
- HPA: Enabled (3-6 replicas)
- Network Policy: Enabled

### Production
- Rolling Update: maxUnavailable: 0%, maxSurge: 33%
- Progress Deadline: 600 seconds
- Replicas: 3
- Resources: CPU 500m-1000m, Memory 1Gi-2Gi
- Security: Maximum security with read-only filesystem
- HPA: Enabled (3-10 replicas)
- Network Policy: Enabled

## Application Type Support

The templates support all application types through dynamic configuration:

- **react-frontend**: Port 3000, static serving, build-time env vars
- **springboot-backend**: Port 8080, database integration, actuator health checks
- **django-backend**: Django ORM, REST framework, admin interface
- **nest-backend**: TypeScript, decorators, dependency injection
- **Generic applications**: Configurable for any application type

## Multi-Cluster Targeting

- **Development/Staging**: Target cluster (6be4e15d-52f9-431d-84ec-ec8cad0dff2d)
- **Production**: Production cluster (e9d23ae8-213c-4746-b379-330f85c0a0cf)
- **ArgoCD Management**: Management cluster (158b6a47-3e7e-4dca-af0f-05a6e07115af)

## Usage

The Kustomize templates are processed by the GitOps automation system to generate project-specific overlays with environment-aware configurations. All deployment operations use Kustomize as the single deployment method, ensuring consistent and reliable GitOps practices.
