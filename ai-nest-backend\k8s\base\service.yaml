apiVersion: v1
kind: Service
metadata:
  name: ai-nest-backend-service
  namespace: ai-nest-backend-dev
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-nest-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "AI Nest Backend"
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  type: LoadBalancer
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/managed-by: argocd
