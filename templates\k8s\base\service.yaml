apiVersion: v1
kind: Service
metadata:
  name: RESOURCE_NAME-service
  namespace: NAMESPACE
  labels:
    app: RESOURCE_NAME
    app.kubernetes.io/name: RESOURCE_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: RESOURCE_NAME
    environment: ENVIRONMENT
    # Display name for human-readable identification
    app.display-name: "APP_NAME"
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  type: SERVICE_TYPE
  ports:
  - port: CONTAINER_PORT
    targetPort: CONTAINER_PORT
    protocol: TCP
    name: http
  selector:
    app: RESOURCE_NAME
    app.kubernetes.io/name: RESOURCE_NAME
    app.kubernetes.io/managed-by: argocd
