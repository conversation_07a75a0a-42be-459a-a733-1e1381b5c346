apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend
  namespace: ai-nest-backend-dev
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-nest-backend
    environment: dev
    deployment.strategy: rolling-update
    # Display name for human-readable identification
    app.display-name: "AI Nest Backend"
  annotations:
    deployment.kubernetes.io/revision: "1"
    argocd.argoproj.io/sync-wave: "2"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
      maxSurge: 50%
  progressDeadlineSeconds: 120
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ai-nest-backend
      app.kubernetes.io/name: ai-nest-backend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-nest-backend
        app.kubernetes.io/name: ai-nest-backend
        app.kubernetes.io/component: nest-backend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        app.kubernetes.io/part-of: ai-nest-backend
        environment: dev
        deployment.strategy: rolling-update
        # Display name for human-readable identification
        app.display-name: "AI Nest Backend"
    spec:
      # Image pull secrets for private container registries
      imagePullSecrets:
      - name: digitalocean-registry
      securityContext:
        runAsNonRoot: false
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      containers:
      - name: ai-nest-backend
        image: registry.digitalocean.com/doks-registry/ai-nest-backend:latest
        imagePullPolicy: Always
        securityContext:
          runAsNonRoot: false
          readOnlyRootFilesystem: false
          allowPrivilegeEscalation: true
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        startupProbe:
          httpGet:
            path: /health
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: STARTUP_PROBE_PERIOD
          timeoutSeconds: STARTUP_PROBE_TIMEOUT
          failureThreshold: 10
          successThreshold: STARTUP_PROBE_SUCCESS_THRESHOLD
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        env:
        - name: NODE_ENV
          value: dev
        - name: PORT
          value: "3000"
        envFrom:
        - configMapRef:
            name: ai-nest-backend-config
        - secretRef:
            name: ai-nest-backend-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
