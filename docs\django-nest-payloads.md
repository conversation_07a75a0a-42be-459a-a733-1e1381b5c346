# Django and NestJS Backend Deployment Payloads

This document provides sample payloads for deploying Django and NestJS backend applications through the GitOps automation system.

## Django Backend Sample Payload

### Repository Dispatch Event (CI/CD Integration)

#### Basic Payload (using default secrets)
```json
{
  "app_name": "Django User API",
  "project_id": "django-user-api",
  "application_type": "django-backend",
  "environment": "dev",
  "docker_image": "myorg/django-user-api",
  "docker_tag": "v1.0.0",
  "source_repo": "myorg/django-user-api",
  "source_branch": "main",
  "commit_sha": "abc123def456"
}
```

#### Advanced Payload (with custom secrets)
```json
{
  "app_name": "Django User API",
  "project_id": "django-user-api",
  "application_type": "django-backend",
  "environment": "dev",
  "docker_image": "myorg/django-user-api",
  "docker_tag": "v1.0.0",
  "source_repo": "myorg/django-user-api",
  "source_branch": "main",
  "commit_sha": "abc123def456",
  "secrets_encoded": "eyJKV1RfU0VDUkVUIjoibXktand0LXNlY3JldCIsIkRCX1BBU1NXT1JEIjoibXktZGItcGFzc3dvcmQiLCJTTVRQX1VTRVIiOiJteS1zbXRwLXVzZXIiLCJTTVRQX1BBU1MiOiJteS1zbXRwLXBhc3MiLCJHT09HTEVfQ0xJRU5UX0lEIjoibXktZ29vZ2xlLWNsaWVudC1pZCIsIkdPT0dMRV9DTElFTlRfU0VDUkVUIjoibXktZ29vZ2xlLWNsaWVudC1zZWNyZXQifQ=="
}
```

### Django Backend CI/CD Workflow Example

```yaml
# .github/workflows/deploy.yml in Django app repository
name: Deploy Django API
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Build and Push Docker Image
        run: |
          docker build -t myorg/django-user-api:${{ github.sha }} .
          docker push myorg/django-user-api:${{ github.sha }}
      
      - name: Deploy to GitOps
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "Django User API",
              "project_id": "django-user-api",
              "application_type": "django-backend",
              "environment": "production",
              "docker_image": "myorg/django-user-api",
              "docker_tag": "${{ github.sha }}",
              "source_repo": "${{ github.repository }}",
              "source_branch": "${{ github.ref_name }}",
              "commit_sha": "${{ github.sha }}"
            }
```

### Django Configuration Details

- **Default Port**: 8000
- **Health Check Path**: `/health/`
- **Database**: PostgreSQL (automatically configured)
- **Environment Variables**:
  - `DJANGO_SETTINGS_MODULE`: Auto-configured based on environment
  - `DEBUG`: `True` for dev, `False` for staging/production
  - `SECRET_KEY`: From secrets
  - `DATABASE_URL`: Auto-configured PostgreSQL connection
  - `ALLOWED_HOSTS`: Configurable
  - `CORS_ALLOWED_ORIGINS`: Configurable

## NestJS Backend Sample Payload

### Repository Dispatch Event (CI/CD Integration)

#### Basic Payload (using default secrets)
```json
{
  "app_name": "NestJS Auth Service",
  "project_id": "nest-auth-service",
  "application_type": "nest-backend",
  "environment": "dev",
  "docker_image": "myorg/nest-auth-service",
  "docker_tag": "v2.1.0",
  "source_repo": "myorg/nest-auth-service",
  "source_branch": "main",
  "commit_sha": "def456ghi789"
}
```

#### Advanced Payload (with custom secrets)
```json
{
  "app_name": "NestJS Auth Service",
  "project_id": "nest-auth-service",
  "application_type": "nest-backend",
  "environment": "dev",
  "docker_image": "myorg/nest-auth-service",
  "docker_tag": "v2.1.0",
  "source_repo": "myorg/nest-auth-service",
  "source_branch": "main",
  "commit_sha": "def456ghi789",
  "secrets_encoded": "eyJKV1RfU0VDUkVUIjoibXktand0LXNlY3JldCIsIkRCX1BBU1NXT1JEIjoibXktZGItcGFzc3dvcmQiLCJTTVRQX1VTRVIiOiJteS1zbXRwLXVzZXIiLCJTTVRQX1BBU1MiOiJteS1zbXRwLXBhc3MiLCJHT09HTEVfQ0xJRU5UX0lEIjoibXktZ29vZ2xlLWNsaWVudC1pZCIsIkdPT0dMRV9DTElFTlRfU0VDUkVUIjoibXktZ29vZ2xlLWNsaWVudC1zZWNyZXQifQ=="
}
```

### NestJS Backend CI/CD Workflow Example

```yaml
# .github/workflows/deploy.yml in NestJS app repository
name: Deploy NestJS Service
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Build and Push Docker Image
        run: |
          docker build -t myorg/nest-auth-service:${{ github.sha }} .
          docker push myorg/nest-auth-service:${{ github.sha }}
      
      - name: Deploy to GitOps
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "NestJS Auth Service",
              "project_id": "nest-auth-service",
              "application_type": "nest-backend",
              "environment": "production",
              "docker_image": "myorg/nest-auth-service",
              "docker_tag": "${{ github.sha }}",
              "source_repo": "${{ github.repository }}",
              "source_branch": "${{ github.ref_name }}",
              "commit_sha": "${{ github.sha }}"
            }
```

### NestJS Configuration Details

- **Default Port**: 3000
- **Health Check Path**: `/health`
- **Database**: PostgreSQL (automatically configured)
- **Environment Variables**:
  - `NODE_ENV`: Auto-configured based on environment
  - `APP_PORT`: Container port (3000)
  - `DATABASE_URL`: Auto-configured PostgreSQL connection
  - `JWT_SECRET`: From secrets
  - `CORS_ORIGIN`: Configurable
  - `CORS_CREDENTIALS`: `true`

## Creating the secrets_encoded Payload

The `secrets_encoded` field contains a base64-encoded JSON object with your application secrets. Here's how to create it:

### Step 1: Create the secrets JSON
```json
{
  "JWT_SECRET": "my-jwt-secret",
  "DB_PASSWORD": "my-db-password",
  "SMTP_USER": "my-smtp-user",
  "SMTP_PASS": "my-smtp-pass",
  "GOOGLE_CLIENT_ID": "my-google-client-id",
  "GOOGLE_CLIENT_SECRET": "my-google-client-secret"
}
```

### Step 2: Base64 encode the JSON
```bash
# Using command line
echo '{"JWT_SECRET":"my-jwt-secret","DB_PASSWORD":"my-db-password","SMTP_USER":"my-smtp-user","SMTP_PASS":"my-smtp-pass","GOOGLE_CLIENT_ID":"my-google-client-id","GOOGLE_CLIENT_SECRET":"my-google-client-secret"}' | base64 -w 0

# Using Node.js
node -e "console.log(Buffer.from(JSON.stringify({JWT_SECRET:'my-jwt-secret',DB_PASSWORD:'my-db-password',SMTP_USER:'my-smtp-user',SMTP_PASS:'my-smtp-pass',GOOGLE_CLIENT_ID:'my-google-client-id',GOOGLE_CLIENT_SECRET:'my-google-client-secret'})).toString('base64'))"

# Using Python
python3 -c "import json, base64; print(base64.b64encode(json.dumps({'JWT_SECRET':'my-jwt-secret','DB_PASSWORD':'my-db-password','SMTP_USER':'my-smtp-user','SMTP_PASS':'my-smtp-pass','GOOGLE_CLIENT_ID':'my-google-client-id','GOOGLE_CLIENT_SECRET':'my-google-client-secret'}).encode()).decode())"
```

### Step 3: Use in CI/CD workflow
```yaml
- name: Deploy to GitOps
  uses: peter-evans/repository-dispatch@v2
  with:
    token: ${{ secrets.GITOPS_TOKEN }}
    repository: ChidhagniConsulting/gitops-argocd-apps
    event-type: deploy-to-argocd
    client-payload: |
      {
        "app_name": "My Django App",
        "project_id": "my-django-app",
        "application_type": "django-backend",
        "environment": "production",
        "docker_image": "myorg/my-django-app",
        "docker_tag": "${{ github.sha }}",
        "secrets_encoded": "${{ secrets.GITOPS_SECRETS_ENCODED }}"
      }
```

**Note**: Store the base64-encoded secrets as a GitHub secret (e.g., `GITOPS_SECRETS_ENCODED`) in your application repository.

## Testing the Payloads

### Using curl to test Django backend deployment:

```bash
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "Test Django API",
      "project_id": "test-django-api",
      "application_type": "django-backend",
      "environment": "dev",
      "docker_image": "python",
      "docker_tag": "3.11-slim",
      "source_repo": "test/django-api",
      "source_branch": "main",
      "commit_sha": "test123"
    }
  }'
```

### Using curl to test NestJS backend deployment:

```bash
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "Test NestJS Service",
      "project_id": "test-nest-service",
      "application_type": "nest-backend",
      "environment": "dev",
      "docker_image": "node",
      "docker_tag": "18-alpine",
      "source_repo": "test/nest-service",
      "source_branch": "main",
      "commit_sha": "test456"
    }
  }'
```

## Resource Requirements

### Django Backend
- **Development**: 256Mi-512Mi memory, 100m-250m CPU
- **Staging**: 512Mi-1Gi memory, 250m-500m CPU  
- **Production**: 1Gi-2Gi memory, 500m-1000m CPU

### NestJS Backend
- **Development**: 256Mi-512Mi memory, 100m-250m CPU
- **Staging**: 512Mi-1Gi memory, 250m-500m CPU
- **Production**: 1Gi-2Gi memory, 500m-1000m CPU

Both application types automatically include PostgreSQL database setup and comprehensive secret management for JWT, SMTP, and OAuth configurations.
