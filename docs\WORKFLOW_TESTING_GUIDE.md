# GitOps Workflow Testing Guide - Dynamic Multi-Application Connectivity

## Overview

This guide provides instructions for testing the updated GitOps workflow with the dynamic multi-application connectivity features implemented in the `multi-stage-deployment-implementation` branch.

## Workflow Modifications Made

### ✅ **Branch Configuration Updated**

The workflow has been temporarily modified to work with your current branch:

1. **Checkout Actions**: Updated to use `ref: multi-stage-deployment-implementation`
2. **Git Push**: Configured to push to `multi-stage-deployment-implementation` branch
3. **Manual Trigger**: Added `workflow_dispatch` for manual testing

### ✅ **Local Validation Completed**

The local validation test has **PASSED** with the following results:

- ✅ Script executed successfully
- ✅ All expected files generated
- ✅ Configuration extraction validated:
  - Container port 3000 ✅
  - Health check path "/" ✅  
  - Database disabled ✅
  - Service port 3000 ✅
  - Application type react-frontend ✅

## Testing Options

### Option 1: Manual Workflow Trigger (Recommended)

Use the GitHub Actions web interface to manually trigger the workflow:

1. **Go to GitHub Actions tab** in your repository
2. **Select** "🚀 Deploy from CI/CD Pipeline (Kustomize)" workflow
3. **Click** "Run workflow" button
4. **Select** branch: `multi-stage-deployment-implementation`
5. **Choose** test scenario: `react-frontend`
6. **Click** "Run workflow"

### Option 2: Repository Dispatch Event

Use the test script to trigger via repository dispatch:

```bash
# Set your GitHub token
export GITHUB_TOKEN=your_github_token_here

# Run the dispatch test
python scripts/test-workflow-dispatch.py
```

### Option 3: Direct API Call

Trigger using curl command:

```bash
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "React Frontend Test",
      "project_id": "react-frontend-test",
      "application_type": "react-frontend", 
      "environment": "staging",
      "docker_image": "registry.digitalocean.com/doks-registry/ai-react-frontend",
      "docker_tag": "v1.0.0",
      "source_repo": "ChidhagniConsulting/ai-react-frontend",
      "source_branch": "main",
      "commit_sha": "test123abc456",
      "secrets_encoded": "eyJjb250YWluZXJfcG9ydCI6IDMwMDAsICJoZWFsdGhfY2hlY2tfcGF0aCI6ICIvIiwgImVuYWJsZV9kYXRhYmFzZSI6IGZhbHNlLCAiSldUX1NFQ1JFVCI6ICJ0ZXN0LWp3dC1zZWNyZXQiLCAiR09PR0xFX0NMSUVOVF9JRCI6ICJ0ZXN0LWdvb2dsZS1jbGllbnQtaWQiLCAiR09PR0xFX0NMSUVOVF9TRUNSRVQiOiAidGVzdC1nb29nbGUtY2xpZW50LXNlY3JldCJ9"
    }
  }'
```

## Expected Workflow Execution

### **Job 1: validate-dispatch**
- ✅ Validates the dispatch payload
- ✅ Extracts configuration from secrets_encoded
- ✅ Outputs all parameters for subsequent jobs

### **Job 2: generate-kustomize-manifests**  
- ✅ Checks out the `multi-stage-deployment-implementation` branch
- ✅ Executes the updated `generate-manifests-cicd.py` script
- ✅ Generates Kustomize overlays with dynamic configuration
- ✅ Creates ArgoCD application manifest

### **Job 3: commit-and-push**
- ✅ Commits generated files to the repository
- ✅ Pushes changes to `multi-stage-deployment-implementation` branch

### **Job 4: deploy-argocd-application**
- ✅ Applies the ArgoCD application to the cluster
- ✅ Monitors deployment status

## Validation Checklist

After workflow execution, verify the following:

### ✅ **Generated Files**
- [ ] `react-frontend-test/argocd/application.yaml` created
- [ ] `react-frontend-test/k8s/overlays/staging/` directory created
- [ ] `kustomization.yaml` contains correct configuration
- [ ] Deployment patches include port 3000 and health check "/"

### ✅ **Configuration Extraction**
- [ ] `CONTAINER_PORT=3000` in kustomization.yaml
- [ ] `HEALTH_CHECK_PATH=/` in kustomization.yaml  
- [ ] `ENABLE_DATABASE=false` in kustomization.yaml
- [ ] `SERVICE_PORT=3000` in kustomization.yaml
- [ ] `APPLICATION_TYPE=react-frontend` in kustomization.yaml

### ✅ **Service Discovery**
- [ ] `SERVICE_NAME=react-frontend-test-service`
- [ ] `SERVICE_NAMESPACE=react-frontend-test-staging`
- [ ] `API_URL=http://react-frontend-test-service:3000`

### ✅ **ArgoCD Application**
- [ ] Application name: `react-frontend-test-staging`
- [ ] Path: `react-frontend-test/k8s/overlays/staging`
- [ ] Target namespace: `react-frontend-test-staging`
- [ ] Sync policy configured correctly

### ✅ **Database Exclusion**
- [ ] No database resources generated (k8s directory empty)
- [ ] No PostgreSQL manifests created

## Troubleshooting

### **Common Issues**

1. **Workflow not triggering**
   - Check that you're on the `multi-stage-deployment-implementation` branch
   - Verify GitHub token has repository dispatch permissions
   - Ensure workflow file is committed and pushed

2. **Script execution fails**
   - Check the workflow logs for Python script errors
   - Verify all required arguments are provided
   - Check secrets payload is valid base64 JSON

3. **Files not generated**
   - Check script output for error messages
   - Verify output directory permissions
   - Check if validation steps passed

4. **ArgoCD deployment fails**
   - Verify cluster connectivity
   - Check ArgoCD application syntax
   - Ensure target namespace exists

### **Debug Commands**

```bash
# Check workflow status
gh run list --workflow="deploy-from-cicd.yaml"

# View workflow logs
gh run view <run-id> --log

# Check generated files locally
ls -la react-frontend-test/
cat react-frontend-test/k8s/overlays/staging/kustomization.yaml
```

## Post-Testing Cleanup

After successful testing:

1. **Revert workflow changes** (remove manual trigger, reset branch refs)
2. **Merge changes to main** if testing is successful
3. **Update documentation** with final configuration
4. **Clean up test deployments** from ArgoCD

## Success Criteria

The test is considered successful when:

- ✅ Workflow executes without errors
- ✅ All configuration is correctly extracted from secrets payload
- ✅ Generated manifests contain expected dynamic configuration
- ✅ ArgoCD application is created and syncs successfully
- ✅ Service discovery variables are properly configured
- ✅ Database resources are excluded as expected

## Next Steps After Successful Testing

1. **Production Readiness**: System is ready for production deployments
2. **Multi-Application Scenarios**: Test frontend-backend connectivity
3. **CI/CD Integration**: Integrate with application repositories
4. **Documentation Updates**: Update user guides with dynamic features
5. **Training**: Share new capabilities with development teams

The dynamic multi-application connectivity system is now ready for comprehensive testing and production use!
