#!/usr/bin/env python3
"""
Dynamic Environment Configuration for GitOps Rolling Update System

This module provides environment-specific configurations for the dynamic
multi-stage rolling update deployment system.
"""

import json
import base64
from typing import Dict, Any, Optional


def get_environment_config(environment: str, app_type: str, project_id: str) -> Dict[str, Any]:
    """
    Get comprehensive environment-specific configuration for rolling updates.
    
    Args:
        environment: Target environment (dev, staging, production)
        app_type: Application type (react-frontend, springboot-backend, etc.)
        project_id: Project identifier
    
    Returns:
        Dictionary containing all environment-specific configuration variables
    """
    
    # Base configuration that applies to all environments
    base_config = {
        'PROJECT_ID': project_id,
        'APP_TYPE': app_type,
        'ENVIRONMENT': environment,
        'APP_VERSION': '1.0.0',
        'IMAGE_PULL_POLICY': 'Always',
        'TERMINATION_GRACE_PERIOD': '30',
        'HEALTH_CHECK_SCHEME': 'HTTP',
        'PROMETHEUS_SCRAPE_ENABLED': 'true',
        'PROMETHEUS_SCRAPE_PORT': '8080',
        'PROMETHEUS_SCRAPE_PATH': '/metrics',
    }
    
    # Environment-specific configurations
    env_configs = {
        'dev': {
            # Rolling Update Strategy
            'ROLLING_UPDATE_MAX_UNAVAILABLE': '50%',
            'ROLLING_UPDATE_MAX_SURGE': '50%',
            'ROLLING_UPDATE_PROGRESS_DEADLINE': '120',
            'ROLLING_UPDATE_REVISION_HISTORY': '3',
            
            # Health Checks
            'STARTUP_PROBE_ENABLED': 'false',
            'READINESS_PROBE_INITIAL_DELAY': '5',
            'READINESS_PROBE_PERIOD': '3',
            'READINESS_PROBE_TIMEOUT': '2',
            'READINESS_PROBE_FAILURE_THRESHOLD': '3',
            'READINESS_PROBE_SUCCESS_THRESHOLD': '1',
            'LIVENESS_PROBE_INITIAL_DELAY': '15',
            'LIVENESS_PROBE_PERIOD': '10',
            'LIVENESS_PROBE_TIMEOUT': '5',
            'LIVENESS_PROBE_FAILURE_THRESHOLD': '3',
            'LIVENESS_PROBE_SUCCESS_THRESHOLD': '1',
            
            # Replicas and Scaling
            'REPLICAS_MIN': '2',
            'REPLICAS_MAX': '4',
            'REPLICAS_DEFAULT': '2',
            
            # Resources
            'CPU_REQUEST': '200m',
            'CPU_LIMIT': '500m',
            'MEMORY_REQUEST': '256Mi',
            'MEMORY_LIMIT': '512Mi',
            
            # Security Context
            'SECURITY_RUN_AS_NON_ROOT': 'false',
            'SECURITY_READ_ONLY_ROOT_FS': 'false',
            'SECURITY_ALLOW_PRIVILEGE_ESCALATION': 'true',
            
            # Pod Disruption Budget
            'PDB_ENABLED': 'true',
            'PDB_MAX_UNAVAILABLE': '50%',
            
            # HPA
            'HPA_ENABLED': 'false',
            'HPA_MIN_REPLICAS': '2',
            'HPA_MAX_REPLICAS': '4',
            'HPA_TARGET_CPU_UTILIZATION': '70',
            'HPA_TARGET_MEMORY_UTILIZATION': '80',
            'HPA_SCALE_DOWN_STABILIZATION': '300',
            'HPA_SCALE_UP_STABILIZATION': '60',
            'HPA_SCALE_DOWN_PERCENT': '10',
            'HPA_SCALE_UP_PERCENT': '50',
            'HPA_SCALE_DOWN_PODS': '1',
            'HPA_SCALE_UP_PODS': '2',
            'HPA_SCALE_DOWN_PERIOD': '60',
            'HPA_SCALE_UP_PERIOD': '30',
            
            # Resource Management
            'RESOURCE_QUOTA_ENABLED': 'true',
            'LIMIT_RANGE_ENABLED': 'true',
            'NETWORK_POLICY_ENABLED': 'false',
        },
        
        'staging': {
            # Rolling Update Strategy
            'ROLLING_UPDATE_MAX_UNAVAILABLE': '25%',
            'ROLLING_UPDATE_MAX_SURGE': '25%',
            'ROLLING_UPDATE_PROGRESS_DEADLINE': '300',
            'ROLLING_UPDATE_REVISION_HISTORY': '5',
            
            # Health Checks
            'STARTUP_PROBE_ENABLED': 'true',
            'STARTUP_PROBE_INITIAL_DELAY': '10',
            'STARTUP_PROBE_PERIOD': '5',
            'STARTUP_PROBE_TIMEOUT': '3',
            'STARTUP_PROBE_FAILURE_THRESHOLD': '5',
            'STARTUP_PROBE_SUCCESS_THRESHOLD': '1',
            'READINESS_PROBE_INITIAL_DELAY': '10',
            'READINESS_PROBE_PERIOD': '5',
            'READINESS_PROBE_TIMEOUT': '3',
            'READINESS_PROBE_FAILURE_THRESHOLD': '3',
            'READINESS_PROBE_SUCCESS_THRESHOLD': '1',
            'LIVENESS_PROBE_INITIAL_DELAY': '30',
            'LIVENESS_PROBE_PERIOD': '10',
            'LIVENESS_PROBE_TIMEOUT': '5',
            'LIVENESS_PROBE_FAILURE_THRESHOLD': '3',
            'LIVENESS_PROBE_SUCCESS_THRESHOLD': '1',
            
            # Replicas and Scaling
            'REPLICAS_MIN': '3',
            'REPLICAS_MAX': '6',
            'REPLICAS_DEFAULT': '3',
            
            # Resources
            'CPU_REQUEST': '300m',
            'CPU_LIMIT': '800m',
            'MEMORY_REQUEST': '512Mi',
            'MEMORY_LIMIT': '1Gi',
            
            # Security Context
            'SECURITY_RUN_AS_NON_ROOT': 'true',
            'SECURITY_RUN_AS_USER': '1000',
            'SECURITY_RUN_AS_GROUP': '1000',
            'SECURITY_READ_ONLY_ROOT_FS': 'true',
            'SECURITY_ALLOW_PRIVILEGE_ESCALATION': 'false',
            
            # Pod Disruption Budget
            'PDB_ENABLED': 'true',
            'PDB_MAX_UNAVAILABLE': '25%',
            
            # HPA
            'HPA_ENABLED': 'true',
            'HPA_MIN_REPLICAS': '3',
            'HPA_MAX_REPLICAS': '6',
            'HPA_TARGET_CPU_UTILIZATION': '70',
            'HPA_TARGET_MEMORY_UTILIZATION': '80',
            'HPA_SCALE_DOWN_STABILIZATION': '300',
            'HPA_SCALE_UP_STABILIZATION': '60',
            'HPA_SCALE_DOWN_PERCENT': '10',
            'HPA_SCALE_UP_PERCENT': '25',
            'HPA_SCALE_DOWN_PODS': '1',
            'HPA_SCALE_UP_PODS': '1',
            'HPA_SCALE_DOWN_PERIOD': '60',
            'HPA_SCALE_UP_PERIOD': '30',
            
            # Resource Management
            'RESOURCE_QUOTA_ENABLED': 'true',
            'LIMIT_RANGE_ENABLED': 'true',
            'NETWORK_POLICY_ENABLED': 'true',
            'NETWORK_POLICY_INGRESS_ENABLED': 'true',
            'NETWORK_POLICY_EGRESS_ENABLED': 'true',
        },
        
        'production': {
            # Rolling Update Strategy
            'ROLLING_UPDATE_MAX_UNAVAILABLE': '0%',
            'ROLLING_UPDATE_MAX_SURGE': '33%',
            'ROLLING_UPDATE_PROGRESS_DEADLINE': '600',
            'ROLLING_UPDATE_REVISION_HISTORY': '10',
            
            # Health Checks
            'STARTUP_PROBE_ENABLED': 'true',
            'STARTUP_PROBE_INITIAL_DELAY': '30',
            'STARTUP_PROBE_PERIOD': '10',
            'STARTUP_PROBE_TIMEOUT': '5',
            'STARTUP_PROBE_FAILURE_THRESHOLD': '10',
            'STARTUP_PROBE_SUCCESS_THRESHOLD': '1',
            'READINESS_PROBE_INITIAL_DELAY': '15',
            'READINESS_PROBE_PERIOD': '10',
            'READINESS_PROBE_TIMEOUT': '5',
            'READINESS_PROBE_FAILURE_THRESHOLD': '3',
            'READINESS_PROBE_SUCCESS_THRESHOLD': '1',
            'LIVENESS_PROBE_INITIAL_DELAY': '60',
            'LIVENESS_PROBE_PERIOD': '30',
            'LIVENESS_PROBE_TIMEOUT': '10',
            'LIVENESS_PROBE_FAILURE_THRESHOLD': '3',
            'LIVENESS_PROBE_SUCCESS_THRESHOLD': '1',
            
            # Replicas and Scaling
            'REPLICAS_MIN': '3',
            'REPLICAS_MAX': '10',
            'REPLICAS_DEFAULT': '3',
            
            # Resources
            'CPU_REQUEST': '500m',
            'CPU_LIMIT': '1000m',
            'MEMORY_REQUEST': '1Gi',
            'MEMORY_LIMIT': '2Gi',
            
            # Security Context
            'SECURITY_RUN_AS_NON_ROOT': 'true',
            'SECURITY_RUN_AS_USER': '1000',
            'SECURITY_RUN_AS_GROUP': '1000',
            'SECURITY_FS_GROUP': '1000',
            'SECURITY_READ_ONLY_ROOT_FS': 'true',
            'SECURITY_ALLOW_PRIVILEGE_ESCALATION': 'false',
            'SECURITY_CAPABILITIES_DROP': 'ALL',
            
            # Pod Disruption Budget
            'PDB_ENABLED': 'true',
            'PDB_MAX_UNAVAILABLE': '1',
            
            # HPA
            'HPA_ENABLED': 'true',
            'HPA_MIN_REPLICAS': '3',
            'HPA_MAX_REPLICAS': '10',
            'HPA_TARGET_CPU_UTILIZATION': '70',
            'HPA_TARGET_MEMORY_UTILIZATION': '80',
            'HPA_SCALE_DOWN_STABILIZATION': '300',
            'HPA_SCALE_UP_STABILIZATION': '60',
            'HPA_SCALE_DOWN_PERCENT': '5',
            'HPA_SCALE_UP_PERCENT': '10',
            'HPA_SCALE_DOWN_PODS': '1',
            'HPA_SCALE_UP_PODS': '1',
            'HPA_SCALE_DOWN_PERIOD': '120',
            'HPA_SCALE_UP_PERIOD': '60',
            
            # Resource Management
            'RESOURCE_QUOTA_ENABLED': 'true',
            'LIMIT_RANGE_ENABLED': 'true',
            'NETWORK_POLICY_ENABLED': 'true',
            'NETWORK_POLICY_INGRESS_ENABLED': 'true',
            'NETWORK_POLICY_EGRESS_ENABLED': 'true',
        }
    }
    
    # Get environment-specific config
    env_config = env_configs.get(environment, env_configs['dev'])
    
    # Merge base config with environment config
    config = {**base_config, **env_config}
    
    # Add application-type-specific configurations
    app_type_config = get_app_type_config(app_type, environment)
    config.update(app_type_config)

    # Add database configuration
    db_config = get_database_config(environment, project_id)
    config.update(db_config)

    return config


def get_app_type_config(app_type: str, environment: str) -> Dict[str, Any]:
    """Get application-type-specific configuration."""
    
    app_configs = {
        'react-frontend': {
            'CONTAINER_PORT': '3000',
            'HEALTH_CHECK_PATH': '/health',
            'HEALTH_CHECK_PORT': '3000',
            'SERVICE_TYPE': 'LoadBalancer',
        },
        'springboot-backend': {
            'CONTAINER_PORT': '8080',
            'HEALTH_CHECK_PATH': '/actuator/health',
            'HEALTH_CHECK_PORT': '8080',
            'SERVICE_TYPE': 'LoadBalancer',
        },
        'django-backend': {
            'CONTAINER_PORT': '8000',
            'HEALTH_CHECK_PATH': '/health/',
            'HEALTH_CHECK_PORT': '8000',
            'SERVICE_TYPE': 'LoadBalancer',
        },
        'nest-backend': {
            'CONTAINER_PORT': '3000',
            'HEALTH_CHECK_PATH': '/health',
            'HEALTH_CHECK_PORT': '3000',
            'SERVICE_TYPE': 'LoadBalancer',
        }
    }
    
    return app_configs.get(app_type, {
        'CONTAINER_PORT': '8080',
        'HEALTH_CHECK_PATH': '/health',
        'HEALTH_CHECK_PORT': '8080',
        'SERVICE_TYPE': 'LoadBalancer',
    })


def get_cluster_config(environment: str) -> Dict[str, str]:
    """Get cluster-specific configuration."""
    
    cluster_mappings = {
        "dev": {
            "server": "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com",
            "name": "dev-staging-cluster",
            "cluster_id": "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        },
        "staging": {
            "server": "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com",
            "name": "dev-staging-cluster",
            "cluster_id": "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        },
        "production": {
            "server": "https://e9d23ae8-213c-4746-b379-330f85c0a0cf.k8s.ondigitalocean.com",
            "name": "production-cluster",
            "cluster_id": "e9d23ae8-213c-4746-b379-330f85c0a0cf"
        }
    }
    
    return cluster_mappings.get(environment, cluster_mappings["production"])


def get_database_config(environment: str, project_id: str) -> Dict[str, str]:
    """Get database configuration for DigitalOcean managed PostgreSQL."""

    # DigitalOcean Managed PostgreSQL Configuration
    # All environments use the same managed database with different database names
    base_db_config = {
        "DB_HOST": "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com",
        "DB_PORT": "25060",
        "DB_USER": "spring_dev_user",
        "DB_SSL_MODE": "require",
        "DB_CONNECTION_TIMEOUT": "30",
        "DB_MAX_CONNECTIONS": "20",
        "DB_MIN_CONNECTIONS": "5"
    }

    # Environment-specific database names
    db_name_mappings = {
        "dev": "spring_dev_db",
        "staging": f"{project_id.replace('-', '_')}_staging_db",
        "production": f"{project_id.replace('-', '_')}_production_db"
    }

    # Get environment-specific database name
    db_name = db_name_mappings.get(environment, "spring_dev_db")
    base_db_config["DB_NAME"] = db_name

    return base_db_config


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 4:
        print("Usage: python dynamic-environment-config.py <environment> <app_type> <project_id>")
        sys.exit(1)
    
    environment = sys.argv[1]
    app_type = sys.argv[2]
    project_id = sys.argv[3]
    
    config = get_environment_config(environment, app_type, project_id)
    print(json.dumps(config, indent=2))
