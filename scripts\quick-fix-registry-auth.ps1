# Quick fix script for DigitalOcean Container Registry authentication
# This script provides an interactive way to fix the ImagePullBackOff issue

param(
    [string]$AccessToken
)

Write-Host "🔧 DigitalOcean Container Registry Authentication Quick Fix" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

# Check if access token is provided
if ([string]::IsNullOrEmpty($AccessToken)) {
    Write-Host "📝 Please provide your DigitalOcean Access Token" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "You can get your token from:" -ForegroundColor White
    Write-Host "https://cloud.digitalocean.com/account/api/tokens" -ForegroundColor Blue
    Write-Host ""
    $AccessToken = Read-Host "Enter your DigitalOcean Access Token"
    
    if ([string]::IsNullOrEmpty($AccessToken)) {
        Write-Host "❌ Access token is required. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Set environment variable
$env:DIGITALOCEAN_ACCESS_TOKEN = $AccessToken

Write-Host "✅ Access token set" -ForegroundColor Green

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Blue

try {
    $null = kubectl version --client --output=json 2>$null
    Write-Host "✅ kubectl is available" -ForegroundColor Green
}
catch {
    Write-Host "❌ kubectl is not available. Please install kubectl first." -ForegroundColor Red
    exit 1
}

try {
    $null = doctl version 2>$null
    Write-Host "✅ doctl is available" -ForegroundColor Green
}
catch {
    Write-Host "❌ doctl is not available. Please install doctl first." -ForegroundColor Red
    Write-Host "Download from: https://docs.digitalocean.com/reference/doctl/how-to/install/" -ForegroundColor Blue
    exit 1
}

# Test DigitalOcean authentication
Write-Host "🔐 Testing DigitalOcean authentication..." -ForegroundColor Blue

try {
    doctl auth init --access-token $AccessToken 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Authentication failed"
    }
    Write-Host "✅ DigitalOcean authentication successful" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to authenticate with DigitalOcean" -ForegroundColor Red
    Write-Host "Please check your access token and try again." -ForegroundColor Yellow
    exit 1
}

# Get registry credentials
Write-Host "🔑 Getting registry credentials..." -ForegroundColor Blue

try {
    $dockerConfig = doctl registry docker-config --expiry-seconds 3600 | ConvertFrom-Json
    $registryAuth = $dockerConfig.auths."registry.digitalocean.com".auth
    
    if ([string]::IsNullOrEmpty($registryAuth)) {
        throw "Registry token is empty"
    }
    
    # Decode the base64 token
    $decodedAuth = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($registryAuth))
    $authParts = $decodedAuth.Split(':')
    
    if ($authParts.Length -ne 2) {
        throw "Invalid auth format"
    }
    
    $username = $authParts[0]
    $password = $authParts[1]
    
    Write-Host "✅ Registry credentials obtained" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to get registry credentials: $_" -ForegroundColor Red
    exit 1
}

# Define namespaces to fix
$namespacesToFix = @(
    "ai-spring-backend-dev",
    "ai-nest-backend-dev", 
    "ai-react-frontend-dev"
)

Write-Host "🛠️  Creating registry secrets in namespaces..." -ForegroundColor Blue

$successCount = 0
foreach ($namespace in $namespacesToFix) {
    try {
        Write-Host "  📁 Processing namespace: $namespace" -ForegroundColor White
        
        # Create namespace if it doesn't exist
        $namespaceExists = kubectl get namespace $namespace 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "    ➕ Creating namespace: $namespace" -ForegroundColor Yellow
            kubectl create namespace $namespace
        }
        
        # Delete existing secret if it exists
        $secretExists = kubectl get secret digitalocean-registry -n $namespace 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "    🗑️  Removing existing secret" -ForegroundColor Yellow
            kubectl delete secret digitalocean-registry -n $namespace
        }
        
        # Create the image pull secret
        kubectl create secret docker-registry digitalocean-registry `
            --namespace=$namespace `
            --docker-server=registry.digitalocean.com `
            --docker-username=$username `
            --docker-password=$password `
            --docker-email=unused
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "    ✅ Secret created successfully" -ForegroundColor Green
            $successCount++
        }
        else {
            Write-Host "    ❌ Failed to create secret" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "    ❌ Error processing namespace $namespace : $_" -ForegroundColor Red
    }
}

# Summary
Write-Host ""
Write-Host "📊 Summary:" -ForegroundColor Cyan
Write-Host "  Processed namespaces: $($namespacesToFix.Count)" -ForegroundColor White
Write-Host "  Successful: $successCount" -ForegroundColor Green
Write-Host "  Failed: $($namespacesToFix.Count - $successCount)" -ForegroundColor Red

if ($successCount -eq $namespacesToFix.Count) {
    Write-Host ""
    Write-Host "🎉 All registry secrets created successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "✅ Your ImagePullBackOff issue should now be resolved." -ForegroundColor Green
    Write-Host ""
    Write-Host "🔍 To verify the fix, run:" -ForegroundColor Blue
    Write-Host "    .\scripts\verify-registry-auth.ps1" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🚀 You can now deploy applications that use DigitalOcean registry:" -ForegroundColor Blue
    Write-Host "    kubectl apply -f ai-spring-backend/k8s/base/" -ForegroundColor Yellow
    Write-Host "    kubectl apply -f ai-nest-backend/k8s/base/" -ForegroundColor Yellow
}
else {
    Write-Host ""
    Write-Host "⚠️  Some secrets failed to create. Please check the errors above." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔧 You can try running this script again or create secrets manually:" -ForegroundColor Blue
    Write-Host "    kubectl create secret docker-registry digitalocean-registry \" -ForegroundColor Yellow
    Write-Host "      --namespace=<namespace> \" -ForegroundColor Yellow
    Write-Host "      --docker-server=registry.digitalocean.com \" -ForegroundColor Yellow
    Write-Host "      --docker-username=$username \" -ForegroundColor Yellow
    Write-Host "      --docker-password=<password> \" -ForegroundColor Yellow
    Write-Host "      --docker-email=unused" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=" * 60 -ForegroundColor Cyan
