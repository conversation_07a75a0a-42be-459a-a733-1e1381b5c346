apiVersion: v1
kind: Secret
metadata:
  name: ai-nest-backend-secret
  namespace: ai-nest-backend-dev
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-nest-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "AI Nest Backend"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
type: Opaque
data:
  # Authentication Secrets (Base64 encoded)
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=
  
  # Database Secrets (DigitalOcean Managed PostgreSQL)
  DB_PASSWORD: QVZOU18wYll6dDBHWmRreTdyblA4S2w3
  
  # SMTP Secrets
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==
  
  # OAuth Secrets
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=
