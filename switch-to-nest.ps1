# Industry Standard Backend Switch to NestJS
Write-Host "🔄 Switching to NestJS backend..." -ForegroundColor Yellow

$NAMESPACE = "ai-react-frontend-dev"
$CONFIGMAP_NAME = "ai-react-frontend-runtime-config"
$BACKEND_URL = "http://************:3000"
$SERVICE_NAME = "ai-nest-backend-service"
$SERVICE_NAMESPACE = "ai-nest-backend-dev"
$TIMESTAMP = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")

# Create new configuration JSON
$configJson = @"
{
  "currentBackend": "nest",
  "backendUrl": "$BACKEND_URL",
  "environment": "dev",
  "serviceName": "$SERVICE_NAME",
  "namespace": "$SERVICE_NAMESPACE",
  "apiVersion": "v1",
  "lastUpdated": "$TIMESTAMP",
  "supportedBackends": [
    {
      "name": "spring",
      "url": "http://*************:8080",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-dev",
      "status": "ready"
    },
    {
      "name": "nest",
      "url": "http://************:3000",
      "serviceName": "ai-nest-backend-service",
      "namespace": "ai-nest-backend-dev",
      "status": "ready"
    }
  ]
}
"@

# Escape for kubectl
$escapedConfig = $configJson -replace '"', '\"' -replace "`n", "" -replace "`r", ""

Write-Host "📝 Updating ConfigMap..." -ForegroundColor Blue
kubectl patch configmap $CONFIGMAP_NAME -n $NAMESPACE --type merge -p "{`"data`":{`"runtime-config.json`":`"$escapedConfig`"}}"

Write-Host "🔄 Restarting frontend deployment..." -ForegroundColor Blue
kubectl rollout restart deployment ai-react-frontend -n $NAMESPACE

Write-Host "⏳ Waiting for deployment..." -ForegroundColor Blue
kubectl rollout status deployment ai-react-frontend -n $NAMESPACE --timeout=300s

Write-Host "🧪 Testing configuration..." -ForegroundColor Blue
Start-Sleep -Seconds 10

$testResult = kubectl exec deployment/ai-react-frontend -n $NAMESPACE -- curl -s http://localhost:3000/api/config
Write-Host "Current config: $testResult" -ForegroundColor Cyan

if ($testResult -match "nest") {
    Write-Host "✅ Successfully switched to NestJS backend!" -ForegroundColor Green
    Write-Host "🎯 Backend URL: $BACKEND_URL" -ForegroundColor Green
} else {
    Write-Host "❌ Switch verification failed" -ForegroundColor Red
}

Write-Host "🎉 Backend switch process completed!" -ForegroundColor Green
