apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: APP_NAME-hpa
  namespace: NAMESPACE
  labels:
    app: APP_NAME
    app.kubernetes.io/name: APP_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: APP_NAME
    environment: ENVIRONMENT
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: APP_NAME
  minReplicas: HPA_MIN_REPLICAS
  maxReplicas: HPA_MAX_REPLICAS
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: HPA_TARGET_CPU_UTILIZATION
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: HPA_TARGET_MEMORY_UTILIZATION
  behavior:
    scaleDown:
      stabilizationWindowSeconds: HPA_SCALE_DOWN_STABILIZATION
      policies:
      - type: Percent
        value: HPA_SCALE_DOWN_PERCENT
        periodSeconds: HPA_SCALE_DOWN_PERIOD
      - type: Pods
        value: HPA_SCALE_DOWN_PODS
        periodSeconds: HPA_SCALE_DOWN_PERIOD
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: HPA_SCALE_UP_STABILIZATION
      policies:
      - type: Percent
        value: HPA_SCALE_UP_PERCENT
        periodSeconds: HPA_SCALE_UP_PERIOD
      - type: Pods
        value: HPA_SCALE_UP_PODS
        periodSeconds: HPA_SCALE_UP_PERIOD
      selectPolicy: Max
