apiVersion: v1
kind: ResourceQuota
metadata:
  name: APP_NAME-resource-quota
spec:
  hard:
    # Staging resource quotas (moderate)
    requests.cpu: "3000m"
    limits.cpu: "6000m"
    requests.memory: "6Gi"
    limits.memory: "12Gi"
    requests.storage: "50Gi"
    pods: "15"
    services: "5"
    secrets: "10"
    configmaps: "10"
    persistentvolumeclaims: "5"
    services.loadbalancers: "2"
    services.nodeports: "3"
