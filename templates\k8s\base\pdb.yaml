apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: APP_NAME-pdb
  namespace: NAMESPACE
  labels:
    app: APP_NAME
    app.kubernetes.io/name: APP_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: APP_NAME
    environment: ENVIRONMENT
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  maxUnavailable: PDB_MAX_UNAVAILABLE
  selector:
    matchLabels:
      app: APP_NAME
      app.kubernetes.io/name: APP_NAME
      app.kubernetes.io/managed-by: argocd
