apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-nest-backend-config
  namespace: ai-nest-backend-dev
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-nest-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "AI Nest Backend"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "3000"
  APP_NAME: "AI Nest Backend"
  APP_TYPE: "nest-backend"

  # Database Configuration (DigitalOcean Managed PostgreSQL)
  DB_HOST: "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com"
  DB_PORT: "25060"
  DB_NAME: "spring_dev_db"
  DB_USER: "spring_dev_user"
  DB_SSL_MODE: "require"

  # SMTP Configuration
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_FROM: "<EMAIL>"

  # Dynamic Backend Configuration (for frontend applications)
  

  # Frontend-specific environment variables
  

  # Service Discovery Configuration
  SERVICE_NAME: "ai-nest-backend-service"
  SERVICE_NAMESPACE: "ai-nest-backend-dev"
  SERVICE_PORT: "3000"

  # Application-specific configurations will be added by overlays
