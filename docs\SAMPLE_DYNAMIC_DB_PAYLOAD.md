# Sample Dispatch Event Payload with Dynamic Database Configuration

This document provides a complete sample dispatch event payload for testing the GitOps automation with dynamic database configuration for managed DigitalOcean PostgreSQL.

## Complete Sample Payload for Spring Boot Backend

```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "My Spring Boot API",
    "project_id": "my-spring-api",
    "application_type": "springboot-backend",
    "environment": "dev",
    "docker_image": "myorg/spring-api",
    "docker_tag": "v1.2.0",
    "container_port": 8080,
    "source_repo": "myorg/spring-api",
    "source_branch": "main",
    "commit_sha": "abc123def456",
    "db_host": "doas-db-10259328-do-user-2815742-0.m.db.digitalocean.com",
    "db_port": "25060",
    "database_name": "my_spring_api_db",
    "secrets_encoded": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  }
}
```

## Secrets Payload (Base64 Decoded)

The `secrets_encoded` field contains the following JSON (base64 encoded):

```json
{
  "JWT_SECRET": "supersecretkey",
  "DB_PASSWORD": "password",
  "SMTP_USER": "<EMAIL>",
  "SMTP_PASS": "fqactehafmzlltzz",
  "GOOGLE_CLIENT_ID": "1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com",
  "GOOGLE_CLIENT_SECRET": "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"
}
```

## Database Configuration Fields

### New Dynamic Fields

- **`db_host`**: The managed DigitalOcean PostgreSQL host
  - Example: `"doas-db-10259328-do-user-2815742-0.m.db.digitalocean.com"`
  - If not provided, defaults to `"*************"`

- **`db_port`**: The database port
  - Example: `"25060"` (DigitalOcean managed PostgreSQL port)
  - If not provided, defaults to `"5432"` (standard PostgreSQL port)

- **`database_name`**: The specific database name for this project
  - Example: `"my_spring_api_db"`
  - If not provided, defaults to project ID with underscores: `"my_spring_api"`

### Generated ConfigMap Values

The automation will generate the following database configuration in the ConfigMap:

```yaml
data:
  DB_HOST: "doas-db-10259328-do-user-2815742-0.m.db.digitalocean.com"
  DB_PORT: "25060"
  DB_NAME: "my_spring_api_db"
  DB_USER: "postgres"
```

### Generated Secret Values

The automation will generate the following database credentials in the Secret:

```yaml
data:
  DB_PASSWORD: "cGFzc3dvcmQ="  # base64 encoded "password"
  DB_USER: "cG9zdGdyZXM="      # base64 encoded "postgres"
```

## Testing the Payload

### Using curl

```bash
# Set your GitHub token
export GITHUB_TOKEN="your_github_token_here"

# Send the dispatch event
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "My Spring Boot API",
      "project_id": "my-spring-api",
      "application_type": "springboot-backend",
      "environment": "dev",
      "docker_image": "myorg/spring-api",
      "docker_tag": "v1.2.0",
      "container_port": 8080,
      "source_repo": "myorg/spring-api",
      "source_branch": "main",
      "commit_sha": "abc123def456",
      "db_host": "doas-db-10259328-do-user-2815742-0.m.db.digitalocean.com",
      "db_port": "25060",
      "database_name": "my_spring_api_db",
      "secrets_encoded": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    }
  }'
```

### Expected Results

1. **No PostgreSQL cluster manifests** should be generated (no postgres-deployment.yaml, postgres-service.yaml)
2. **ConfigMap** should contain the dynamic database configuration
3. **Secret** should contain the database credentials from the secrets payload
4. **Deployment** should reference the ConfigMap and Secret for database connectivity

## Environment-Specific Examples

### Development Environment
```json
{
  "environment": "dev",
  "db_host": "doas-db-10259328-do-user-2815742-0.m.db.digitalocean.com",
  "db_port": "25060",
  "database_name": "myapp_dev"
}
```

### Staging Environment
```json
{
  "environment": "staging",
  "db_host": "doas-db-10259328-do-user-2815742-0.m.db.digitalocean.com",
  "db_port": "25060",
  "database_name": "myapp_staging"
}
```

### Production Environment
```json
{
  "environment": "production",
  "db_host": "doas-db-production-cluster.m.db.digitalocean.com",
  "db_port": "25060",
  "database_name": "myapp_production"
}
```

## Backward Compatibility

If the new database fields are not provided in the payload:
- `db_host` defaults to `"*************"`
- `db_port` defaults to `"5432"`
- `database_name` defaults to project ID with underscores

This ensures existing payloads continue to work without modification.
