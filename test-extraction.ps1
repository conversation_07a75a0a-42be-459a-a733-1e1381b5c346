#!/usr/bin/env pwsh

# Test script to verify field extraction
$IssueBody = Get-Content "test-issue-body.txt" -Raw

# Function to extract value from issue body (copied from main script)
function Get-IssueValue {
    param(
        [string]$Body,
        [string]$FieldName
    )
    
    $pattern = "### $FieldName\s*\n\s*(.+?)(?=\n###|\n\n|\Z)"
    $match = [regex]::Match($Body, $pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    if ($match.Success) {
        return $match.Groups[1].Value.Trim()
    }
    return ""
}

Write-Host "🧪 Testing Field Extraction" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Test extraction of key fields
$fields = @{
    "Application Name" = ""
    "Project Identifier" = ""
    "Application Type" = ""
    "Backend Type" = ""
    "Runtime Configuration" = ""
    "Environment" = ""
    "Container Port" = ""
}

foreach ($fieldName in $fields.Keys) {
    $value = Get-IssueValue -Body $IssueBody -FieldName $fieldName
    $fields[$fieldName] = $value
    
    if ($value) {
        Write-Host "✅ $fieldName`: $value" -ForegroundColor Green
    } else {
        Write-Host "❌ $fieldName`: (not found)" -ForegroundColor Red
    }
}

Write-Host "`n🔍 Runtime Configuration Analysis" -ForegroundColor Cyan
$runtimeConfig = $fields["Runtime Configuration"]
if ($runtimeConfig -like "*Enable runtime configuration*") {
    Write-Host "✅ Runtime configuration is enabled" -ForegroundColor Green
    $runtimeEnabled = $true
} else {
    Write-Host "❌ Runtime configuration is not enabled" -ForegroundColor Red
    $runtimeEnabled = $false
}

Write-Host "`n🎯 Backend Type Analysis" -ForegroundColor Cyan
$backendType = $fields["Backend Type"]
if ($backendType) {
    Write-Host "✅ Backend Type: $backendType" -ForegroundColor Green
    
    # Test backend URL mapping
    switch ($backendType.ToLower()) {
        "spring" {
            $backendUrl = "http://*************:8080"
            Write-Host "  🔗 Backend URL: $backendUrl" -ForegroundColor Yellow
        }
        "django" {
            $backendUrl = "http://*************:8000"
            Write-Host "  🔗 Backend URL: $backendUrl" -ForegroundColor Yellow
        }
        "nest" {
            $backendUrl = "http://**************:3000"
            Write-Host "  🔗 Backend URL: $backendUrl" -ForegroundColor Yellow
        }
        default {
            $backendUrl = "http://*************:8080"
            Write-Host "  🔗 Backend URL (default): $backendUrl" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "❌ Backend Type not specified" -ForegroundColor Red
}

Write-Host "`n📋 Summary" -ForegroundColor Cyan
Write-Host "==========" -ForegroundColor Cyan
Write-Host "Application Type: $($fields['Application Type'])" -ForegroundColor White
Write-Host "Backend Type: $backendType" -ForegroundColor White
Write-Host "Runtime Config Enabled: $runtimeEnabled" -ForegroundColor White
Write-Host "Project ID: $($fields['Project Identifier'])" -ForegroundColor White

if ($fields["Application Type"] -eq "react-frontend" -and $runtimeEnabled) {
    Write-Host "`n🎉 Configuration is valid for new approach!" -ForegroundColor Green
    Write-Host "   - React Frontend application ✅" -ForegroundColor Green
    Write-Host "   - Runtime configuration enabled ✅" -ForegroundColor Green
    Write-Host "   - Backend type specified: $backendType ✅" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Configuration does not meet new approach requirements" -ForegroundColor Yellow
}
