# Dynamic Multi-Application Connectivity Guide

This guide demonstrates the enhanced Kustomize-based GitOps system that supports truly dynamic multi-application connectivity with unlimited application types.

## Overview of Dynamic Capabilities

The refactored system now supports:

✅ **Unlimited Application Types**: No hardcoded restrictions - deploy any application type  
✅ **Dynamic Backend Discovery**: Frontend applications can connect to any backend dynamically  
✅ **Configuration-Driven Deployment**: All application-specific settings via `secrets_encoded` payload  
✅ **Scalable Service Discovery**: Automatic service URL generation for any number of applications  
✅ **Backward Compatibility**: Existing deployments continue to work seamlessly  

## Enhanced Repository Dispatch Payload Structure

### New `secrets_encoded` Payload Format

The `secrets_encoded` field now contains a base64-encoded JSON payload with both secrets and application configuration:

```json
{
  "JWT_SECRET": "your-jwt-secret",
  "DB_USER": "app_user", 
  "DB_PASSWORD": "secure_password",
  "SMTP_USER": "<EMAIL>",
  "SMTP_PASS": "smtp_password",
  "GOOGLE_CLIENT_ID": "your-google-client-id",
  "GOOGLE_CLIENT_SECRET": "your-google-client-secret",
  "container_port": 3000,
  "health_check_path": "/api/health",
  "enable_database": true,
  "backend_type": "api-service",
  "backend_project_id": "my-api-backend",
  "backend_port": 8080
}
```

### Dynamic Configuration Extraction

The system now extracts application configuration from the secrets payload instead of command-line arguments:

- **`container_port`**: Overrides the default port for the application
- **`health_check_path`**: Specifies the health check endpoint
- **`enable_database`**: Controls database setup for the application
- **`backend_type`**: Identifier for the backend service type
- **`backend_project_id`**: Project ID of the backend application to connect to
- **`backend_port`**: Port of the backend application

## Dynamic Backend URL Generation

### New Service URL Generation

The system generates backend URLs using a configurable naming convention:

```
http://{backend-project-id}-service.{backend-project-id}-{environment}.svc.cluster.local:{backend-port}
```

### Examples

| Backend Project ID | Environment | Port | Generated URL |
|-------------------|-------------|------|---------------|
| `user-api` | `staging` | `8080` | `http://user-api-service.user-api-staging.svc.cluster.local:8080` |
| `payment-service` | `production` | `3000` | `http://payment-service-service.payment-service-production.svc.cluster.local:3000` |
| `notification-api` | `dev` | `9000` | `http://notification-api-service.notification-api-dev.svc.cluster.local:9000` |

## Complete Example: E-commerce Platform

Let's deploy a complete e-commerce platform with multiple applications:

### 1. Deploy User Authentication API

```bash
# Base64 encode the secrets payload
SECRETS_PAYLOAD=$(echo '{
  "JWT_SECRET": "user-auth-secret-key",
  "DB_USER": "auth_user",
  "DB_PASSWORD": "auth_password",
  "container_port": 8080,
  "health_check_path": "/actuator/health",
  "enable_database": true
}' | base64 -w 0)

# Deploy via repository dispatch
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "User Authentication API",
      "project_id": "user-auth-api",
      "application_type": "spring-boot-api",
      "environment": "staging",
      "docker_image": "myorg/user-auth-api",
      "docker_tag": "v2.1.0",
      "secrets_encoded": "'$SECRETS_PAYLOAD'"
    }
  }'
```

### 2. Deploy Product Catalog Service

```bash
# Base64 encode the secrets payload
SECRETS_PAYLOAD=$(echo '{
  "JWT_SECRET": "catalog-secret-key",
  "DB_USER": "catalog_user",
  "DB_PASSWORD": "catalog_password",
  "container_port": 3000,
  "health_check_path": "/health",
  "enable_database": true
}' | base64 -w 0)

# Deploy via repository dispatch
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "Product Catalog Service",
      "project_id": "product-catalog",
      "application_type": "nodejs-api",
      "environment": "staging",
      "docker_image": "myorg/product-catalog",
      "docker_tag": "v1.5.0",
      "secrets_encoded": "'$SECRETS_PAYLOAD'"
    }
  }'
```

### 3. Deploy Payment Processing Service

```bash
# Base64 encode the secrets payload
SECRETS_PAYLOAD=$(echo '{
  "JWT_SECRET": "payment-secret-key",
  "DB_USER": "payment_user",
  "DB_PASSWORD": "payment_password",
  "STRIPE_SECRET_KEY": "sk_test_...",
  "container_port": 8000,
  "health_check_path": "/health/",
  "enable_database": true
}' | base64 -w 0)

# Deploy via repository dispatch
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "Payment Processing Service",
      "project_id": "payment-service",
      "application_type": "python-django-api",
      "environment": "staging",
      "docker_image": "myorg/payment-service",
      "docker_tag": "v3.0.0",
      "secrets_encoded": "'$SECRETS_PAYLOAD'"
    }
  }'
```

### 4. Deploy E-commerce Frontend (Connecting to User Auth API)

```bash
# Base64 encode the secrets payload with backend configuration
SECRETS_PAYLOAD=$(echo '{
  "GOOGLE_CLIENT_ID": "your-google-client-id",
  "GOOGLE_CLIENT_SECRET": "your-google-client-secret",
  "container_port": 3000,
  "health_check_path": "/",
  "enable_database": false,
  "backend_type": "user-auth",
  "backend_project_id": "user-auth-api",
  "backend_port": 8080
}' | base64 -w 0)

# Deploy via repository dispatch
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "E-commerce Frontend",
      "project_id": "ecommerce-frontend",
      "application_type": "react-spa",
      "environment": "staging",
      "docker_image": "myorg/ecommerce-frontend",
      "docker_tag": "v4.2.0",
      "secrets_encoded": "'$SECRETS_PAYLOAD'"
    }
  }'
```

### 5. Switch Frontend to Connect to Product Catalog

```bash
# Update frontend to connect to product catalog instead
SECRETS_PAYLOAD=$(echo '{
  "GOOGLE_CLIENT_ID": "your-google-client-id",
  "GOOGLE_CLIENT_SECRET": "your-google-client-secret",
  "container_port": 3000,
  "health_check_path": "/",
  "enable_database": false,
  "backend_type": "product-catalog",
  "backend_project_id": "product-catalog",
  "backend_port": 3000
}' | base64 -w 0)

# Redeploy with new backend configuration
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "E-commerce Frontend",
      "project_id": "ecommerce-frontend",
      "application_type": "react-spa",
      "environment": "staging",
      "docker_image": "myorg/ecommerce-frontend",
      "docker_tag": "v4.2.1",
      "secrets_encoded": "'$SECRETS_PAYLOAD'"
    }
  }'
```

## Generated Service Discovery Configuration

For the e-commerce frontend connecting to the user auth API, the system generates:

### ConfigMap Environment Variables
```yaml
data:
  # Application Configuration
  NODE_ENV: "staging"
  PORT: "3000"
  APP_NAME: "ecommerce-frontend"
  APP_TYPE: "react-spa"
  
  # Dynamic Backend Configuration
  API_URL: "http://user-auth-api-service.user-auth-api-staging.svc.cluster.local:8080"
  BACKEND_TYPE: "user-auth"
  BACKEND_PROJECT_ID: "user-auth-api"
  BACKEND_PORT: "8080"
  BACKEND_NAMESPACE: "user-auth-api-staging"
  
  # Frontend-specific environment variables
  REACT_APP_API_URL: "http://user-auth-api-service.user-auth-api-staging.svc.cluster.local:8080"
  REACT_APP_BACKEND_TYPE: "user-auth"
  
  # Service Discovery Configuration
  SERVICE_NAME: "ecommerce-frontend-service"
  SERVICE_NAMESPACE: "ecommerce-frontend-staging"
  SERVICE_PORT: "3000"
```

### Network Policy Rules
```yaml
spec:
  # Allow ingress from other application namespaces
  ingress:
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: Exists
    ports:
    - protocol: TCP
      port: 3000
  
  # Allow egress to backend services
  egress:
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: Exists
    ports:
    - protocol: TCP
      port: 8080  # User Auth API
    - protocol: TCP
      port: 3000  # Product Catalog
    - protocol: TCP
      port: 8000  # Payment Service
```

## Key Benefits of the Dynamic System

### 1. **Unlimited Scalability**
- Deploy any number of applications without code changes
- Support any application type through dynamic configuration
- No hardcoded service mappings or port restrictions

### 2. **Configuration-Driven Deployment**
- All application settings via the `secrets_encoded` payload
- No need to modify deployment scripts for new application types
- Centralized configuration management

### 3. **Dynamic Service Discovery**
- Automatic backend URL generation based on naming conventions
- Support for cross-namespace communication
- Flexible network policy rules for inter-application connectivity

### 4. **Backward Compatibility**
- Existing deployments continue to work without changes
- Legacy backend type mappings maintained for compatibility
- Gradual migration path to the new dynamic system

### 5. **Enhanced Security**
- Network policies control inter-application communication
- Secrets management through base64-encoded payloads
- Environment-specific security configurations

## Migration from Legacy System

Existing applications can be migrated gradually:

1. **Continue using existing deployments** - no immediate changes required
2. **Add secrets payload** - include application configuration in `secrets_encoded`
3. **Remove command-line arguments** - let the system extract from secrets payload
4. **Update backend references** - use `backend_project_id` instead of hardcoded types

The system maintains full backward compatibility while enabling new dynamic capabilities.
