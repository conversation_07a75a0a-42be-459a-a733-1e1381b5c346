apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-nest-backend-dev
  namespace: argocd
  labels:
    app: ai-nest-backend
    environment: dev
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/managed-by: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps.git
    targetRevision: main
    path: ai-nest-backend/k8s/overlays/dev
  destination:
    server: https://kubernetes.default.svc
    namespace: ai-nest-backend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
    - CreateNamespace=true
    - PrunePropagationPolicy=foreground
    - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10