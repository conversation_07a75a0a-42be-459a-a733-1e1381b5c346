param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("spring", "nest", "django")]
    [string]$BackendType
)

# Industry Standard Backend Switching Script
# Usage: .\Switch-Backend.ps1 -BackendType nest

$ErrorActionPreference = "Stop"

$NAMESPACE = "ai-react-frontend-dev"
$CONFIGMAP_NAME = "ai-react-frontend-runtime-config"

Write-Host "🔄 Switching to $BackendType backend..." -ForegroundColor Yellow

# Define backend configurations
switch ($BackendType) {
    "spring" {
        $BACKEND_URL = "http://*************:8080"
        $SERVICE_NAME = "ai-spring-backend-service"
        $SERVICE_NAMESPACE = "ai-spring-backend-dev"
    }
    "nest" {
        $BACKEND_URL = "http://************:3000"
        $SERVICE_NAME = "ai-nest-backend-service"
        $SERVICE_NAMESPACE = "ai-nest-backend-dev"
    }
    "django" {
        $BACKEND_URL = "http://*************:8000"
        $SERVICE_NAME = "ai-django-backend-service"
        $SERVICE_NAMESPACE = "ai-django-backend-dev"
    }
}

# Create new configuration
$TIMESTAMP = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")

$NEW_CONFIG = @{
    currentBackend = $BackendType
    backendUrl = $BACKEND_URL
    environment = "dev"
    serviceName = $SERVICE_NAME
    namespace = $SERVICE_NAMESPACE
    apiVersion = "v1"
    lastUpdated = $TIMESTAMP
    supportedBackends = @(
        @{
            name = "spring"
            url = "http://*************:8080"
            internalUrl = "http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080"
            serviceName = "ai-spring-backend-service"
            namespace = "ai-spring-backend-dev"
            status = "ready"
            healthEndpoint = "/actuator/health"
        },
        @{
            name = "django"
            url = "http://*************:8000"
            internalUrl = "http://ai-django-backend-service.ai-django-backend-dev.svc.cluster.local:8000"
            serviceName = "ai-django-backend-service"
            namespace = "ai-django-backend-dev"
            status = "ready"
            healthEndpoint = "/health"
        },
        @{
            name = "nest"
            url = "http://************:3000"
            internalUrl = "http://ai-nest-backend-service.ai-nest-backend-dev.svc.cluster.local:3000"
            serviceName = "ai-nest-backend-service"
            namespace = "ai-nest-backend-dev"
            status = "ready"
            healthEndpoint = "/health"
        }
    )
} | ConvertTo-Json -Depth 10 -Compress

# Escape quotes for kubectl
$ESCAPED_CONFIG = $NEW_CONFIG -replace '"', '\"'

# Update ConfigMap
Write-Host "📝 Updating ConfigMap..." -ForegroundColor Blue
kubectl patch configmap $CONFIGMAP_NAME -n $NAMESPACE --type merge -p "{`"data`":{`"runtime-config.json`":`"$ESCAPED_CONFIG`"}}"

# Restart frontend deployment
Write-Host "🔄 Restarting frontend deployment..." -ForegroundColor Blue
kubectl rollout restart deployment ai-react-frontend -n $NAMESPACE

# Wait for rollout
Write-Host "⏳ Waiting for deployment to complete..." -ForegroundColor Blue
kubectl rollout status deployment ai-react-frontend -n $NAMESPACE --timeout=300s

# Verify the switch
Write-Host "🧪 Verifying backend switch..." -ForegroundColor Blue
Start-Sleep -Seconds 5

try {
    $CURRENT_CONFIG = kubectl exec deployment/ai-react-frontend -n $NAMESPACE -- curl -s http://localhost:3000/api/config 2>$null

    if ($CURRENT_CONFIG -match "`"currentBackend`":`"$BackendType`"") {
        Write-Host "✅ Successfully switched to $BackendType backend!" -ForegroundColor Green
        Write-Host "🎯 Backend URL: $BACKEND_URL" -ForegroundColor Green
        Write-Host "📊 Service: $SERVICE_NAME in $SERVICE_NAMESPACE" -ForegroundColor Green
    } else {
        Write-Host "❌ Backend switch verification failed" -ForegroundColor Red
        Write-Host "Current config: $CURRENT_CONFIG" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "❌ Failed to verify backend switch: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 Backend switch completed successfully!" -ForegroundColor Green
