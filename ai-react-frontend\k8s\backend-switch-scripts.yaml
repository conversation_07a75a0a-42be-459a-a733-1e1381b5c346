apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-switch-scripts
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: switch-scripts
data:
  switch-to-spring.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to Spring Boot backend..."

    kubectl patch configmap ai-react-frontend-env -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "env-config.js": "window._env_ = {\n  REACT_APP_BACKEND_URL: \"http://*************:8080\",\n  REACT_APP_CURRENT_BACKEND: \"spring\",\n  REACT_APP_ENVIRONMENT: \"dev\",\n  REACT_APP_API_VERSION: \"v1\",\n  REACT_APP_SERVICE_NAME: \"ai-spring-backend-service\",\n  REACT_APP_BACKEND_NAMESPACE: \"ai-spring-backend-dev\",\n  REACT_APP_GOOGLE_OAUTH_URL: \"http://*************:8080/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect\",\n  REACT_APP_USE_RUNTIME_CONFIG: \"true\",\n  REACT_APP_DEBUG_MODE: \"true\",\n  REACT_APP_LAST_UPDATED: \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"\n};"
      }
    }'

    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to Spring Boot backend (http://*************:8080)"

  switch-to-django.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to Django backend..."

    kubectl patch configmap ai-react-frontend-env -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "env-config.js": "window._env_ = {\n  REACT_APP_BACKEND_URL: \"http://152.42.156.72:8000\",\n  REACT_APP_CURRENT_BACKEND: \"django\",\n  REACT_APP_ENVIRONMENT: \"dev\",\n  REACT_APP_API_VERSION: \"v1\",\n  REACT_APP_SERVICE_NAME: \"ai-django-backend-service\",\n  REACT_APP_BACKEND_NAMESPACE: \"ai-django-backend-dev\",\n  REACT_APP_GOOGLE_OAUTH_URL: \"http://152.42.156.72:8000/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect\",\n  REACT_APP_USE_RUNTIME_CONFIG: \"true\",\n  REACT_APP_DEBUG_MODE: \"true\",\n  REACT_APP_LAST_UPDATED: \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"\n};"
      }
    }'

    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to Django backend (http://152.42.156.72:8000)"

  switch-to-nest.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to NestJS backend..."

    # Check if NestJS backend is ready
    if ! kubectl get service ai-nest-backend-service -n ai-nest-backend-dev >/dev/null 2>&1; then
      echo "❌ NestJS backend service not found. Please deploy NestJS backend first."
      exit 1
    fi

    kubectl patch configmap ai-react-frontend-env -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "env-config.js": "window._env_ = {\n  REACT_APP_BACKEND_URL: \"http://174.138.121.78:3000\",\n  REACT_APP_CURRENT_BACKEND: \"nest\",\n  REACT_APP_ENVIRONMENT: \"dev\",\n  REACT_APP_API_VERSION: \"v1\",\n  REACT_APP_SERVICE_NAME: \"ai-nest-backend-service\",\n  REACT_APP_BACKEND_NAMESPACE: \"ai-nest-backend-dev\",\n  REACT_APP_GOOGLE_OAUTH_URL: \"http://174.138.121.78:3000/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect\",\n  REACT_APP_USE_RUNTIME_CONFIG: \"true\",\n  REACT_APP_DEBUG_MODE: \"true\",\n  REACT_APP_LAST_UPDATED: \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"\n};"
      }
    }'

    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to NestJS backend (http://174.138.121.78:3000)"

  get-current-backend.sh: |
    #!/bin/bash
    echo "📋 Current Backend Configuration:"
    kubectl get configmap ai-react-frontend-env -n ai-react-frontend-dev -o jsonpath='{.data.env-config\.js}'

    echo -e "\n🔍 Backend Health Status:"
    # Extract backend URL and type from env-config.js
    CURRENT_URL=$(kubectl get configmap ai-react-frontend-env -n ai-react-frontend-dev -o jsonpath='{.data.env-config\.js}' | grep -o 'REACT_APP_BACKEND_URL: "[^"]*"' | cut -d'"' -f2)
    BACKEND_TYPE=$(kubectl get configmap ai-react-frontend-env -n ai-react-frontend-dev -o jsonpath='{.data.env-config\.js}' | grep -o 'REACT_APP_CURRENT_BACKEND: "[^"]*"' | cut -d'"' -f2)

    echo "Backend Type: $BACKEND_TYPE"
    echo "Backend URL: $CURRENT_URL"

    case $BACKEND_TYPE in
      "spring")
        curl -f $CURRENT_URL/actuator/health && echo "✅ Spring Boot is healthy" || echo "❌ Spring Boot is not responding"
        ;;
      "django")
        curl -f $CURRENT_URL/health && echo "✅ Django is healthy" || echo "❌ Django is not responding"
        ;;
      "nest")
        curl -f $CURRENT_URL/health && echo "✅ NestJS is healthy" || echo "❌ NestJS is not responding"
        ;;
    esac

  test-env-config.sh: |
    #!/bin/bash
    echo "🧪 Testing env-config.js accessibility..."

    # Get frontend service URL
    FRONTEND_URL="http://*************:3000"

    echo "Testing env-config.js from: $FRONTEND_URL/env-config.js"
    curl -v $FRONTEND_URL/env-config.js

    echo -e "\n✅ Test completed"
