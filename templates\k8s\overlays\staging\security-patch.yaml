apiVersion: apps/v1
kind: Deployment
metadata:
  name: APP_NAME
spec:
  template:
    spec:
      # Staging security context (enhanced security)
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
      containers:
      - name: APP_NAME
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          readOnlyRootFilesystem: true
          allowPrivilegeEscalation: false
