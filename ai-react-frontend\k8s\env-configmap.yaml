apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-env
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: environment-config
    managed-by: argocd
    environment: dev
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    description: "Runtime environment configuration for dev environment - Django backend"
data:
  env-config.js: |
    window._env_ = {
      // Current active backend (change this to switch backends)
      REACT_APP_BACKEND_URL: "http://*************:8000",  // Django backend
      REACT_APP_CURRENT_BACKEND: "django",

      // Environment configuration
      REACT_APP_ENVIRONMENT: "dev",
      REACT_APP_API_VERSION: "v1",
      REACT_APP_LAST_UPDATED: "2025-07-24T20:15:00Z",

      // Service configuration for dev environment
      REACT_APP_SERVICE_NAME: "ai-django-backend-service",
      REACT_APP_BACKEND_NAMESPACE: "ai-django-backend-dev",

      // OAuth configuration
      REACT_APP_GOOGLE_OAUTH_URL: "http://*************:8000/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect",

      // Feature flags
      REACT_APP_USE_RUNTIME_CONFIG: "true",
      REACT_APP_DEBUG_MODE: "true"
    };

    // Available backend configurations for dev environment:
    //
    // Spring Boot Backend:
    // REACT_APP_BACKEND_URL: "http://64.225.85.162:8080"
    // REACT_APP_CURRENT_BACKEND: "spring"
    // REACT_APP_SERVICE_NAME: "ai-spring-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-spring-backend-dev"
    //
    // Django Backend:
    // REACT_APP_BACKEND_URL: "http://*************:8000"
    // REACT_APP_CURRENT_BACKEND: "django"
    // REACT_APP_SERVICE_NAME: "ai-django-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-django-backend-dev"
    //
    // Nest Backend:
    // REACT_APP_BACKEND_URL: "http://174.138.121.78:3000"
    // REACT_APP_CURRENT_BACKEND: "nest"
    // REACT_APP_SERVICE_NAME: "ai-nest-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-nest-backend-dev"
