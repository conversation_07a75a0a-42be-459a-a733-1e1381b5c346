# Kustomize-Based GitOps Deployment Guide

This guide provides comprehensive instructions for using the Kustomize-based deployment system with dynamic multi-stage rolling update GitOps automation.

## Overview

The GitOps system uses Kustomize as the single deployment method, providing declarative configuration management with environment-specific overlays and comprehensive multi-stage deployment capabilities.

## Architecture

### Directory Structure

```
gitops-argocd-apps/
├── templates/                    # Template resources
│   └── k8s/                # Kustomize base and overlays
│       ├── base/                 # Base Kubernetes manifests
│       │   ├── deployment.yaml   # Base deployment configuration
│       │   ├── service.yaml      # Base service configuration
│       │   ├── configmap.yaml    # Base configmap configuration
│       │   ├── secret.yaml       # Base secret configuration
│       │   ├── namespace.yaml    # Base namespace configuration
│       │   ├── pdb.yaml          # Pod Disruption Budget
│       │   ├── hpa.yaml          # Horizontal Pod Autoscaler
│       │   ├── resource-quota.yaml # Resource Quota
│       │   ├── limit-range.yaml  # Limit Range
│       │   ├── network-policy.yaml # Network Policy
│       │   └── kustomization.yaml # Base kustomization
│       └── overlays/             # Environment-specific overlays
│           ├── dev/              # Development environment
│           ├── staging/          # Staging environment
│           └── production/       # Production environment
└── project-id/                  # Generated project directories
    ├── argocd/                   # ArgoCD configurations
    └── k8s/                # Project-specific Kustomize overlays
        └── overlays/
            └── environment/      # Environment-specific patches
```

## Base Configuration

The base configuration in `k8s/base/` contains placeholder values that are replaced by environment-specific overlays:

### Key Placeholder Variables

- `APP_NAME` - Application name
- `NAMESPACE` - Kubernetes namespace
- `CONTAINER_IMAGE` - Docker image
- `CONTAINER_PORT` - Application port
- `ENVIRONMENT` - Target environment
- `ROLLING_UPDATE_*` - Rolling update strategy parameters
- `*_PROBE_*` - Health check configurations
- `SECURITY_*` - Security context settings
- `RESOURCE_*` - Resource management settings

## Environment-Specific Overlays

### Development Environment

**Characteristics:**
- Fast rolling updates (50%/50%)
- Relaxed security context
- Minimal resource requirements
- Disabled HPA and Network Policy
- TCP health checks for faster startup

**Generated Files:**
- `kustomization.yaml` - Environment configuration
- `deployment-patch.yaml` - Development-specific deployment settings
- `resource-patch.yaml` - Relaxed resource quotas
- `security-patch.yaml` - Development security context
- `disable-hpa-patch.yaml` - Disables HPA
- `disable-network-policy-patch.yaml` - Disables Network Policy

### Staging Environment

**Characteristics:**
- Balanced rolling updates (25%/25%)
- Enhanced security with non-root users
- Moderate resource allocation
- Enabled HPA and PDB
- Comprehensive health checks with startup probe

**Generated Files:**
- `kustomization.yaml` - Environment configuration
- `deployment-patch.yaml` - Staging-specific deployment settings
- `resource-patch.yaml` - Moderate resource quotas
- `security-patch.yaml` - Enhanced security context
- `hpa-patch.yaml` - Horizontal Pod Autoscaler configuration
- `pdb-patch.yaml` - Pod Disruption Budget configuration

### Production Environment

**Characteristics:**
- Zero-downtime rolling updates (0%/33%)
- Maximum security with read-only filesystem
- High resource allocation
- Optimized HPA and PDB settings
- Comprehensive health checks with extended timeouts

**Generated Files:**
- `kustomization.yaml` - Environment configuration
- `deployment-patch.yaml` - Production-specific deployment settings
- `resource-patch.yaml` - High-capacity resource quotas
- `security-patch.yaml` - Maximum security context
- `hpa-patch.yaml` - Production-optimized HPA
- `pdb-patch.yaml` - Production PDB with minimal disruption

## Usage Instructions

### 1. Using Kustomize Mode via CI/CD

Add the `use_kustomize` flag to your repository dispatch payload:

```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "My Application",
    "project_id": "my-app",
    "application_type": "springboot-backend",
    "environment": "production",
    "docker_image": "myorg/my-app",
    "docker_tag": "v1.0.0",
    "container_port": 8080,
    "enable_database": true,
    "use_kustomize": true,
    "secrets_encoded": "base64-encoded-secrets"
  }
}
```

### 2. Using Kustomize Mode via Command Line

```bash
python scripts/generate-manifests-cicd.py \
  --use-kustomize \
  --app-name "My Application" \
  --project-id "my-app" \
  --application-type "springboot-backend" \
  --environment "production" \
  --docker-image "myorg/my-app" \
  --docker-tag "v1.0.0" \
  --container-port 8080 \
  --enable-database \
  --secrets-encoded "base64-encoded-secrets"
```

### 3. Building and Validating Kustomize Configurations

```bash
# Build development environment
kustomize build my-app/k8s/overlays/dev

# Build staging environment
kustomize build my-app/k8s/overlays/staging

# Build production environment
kustomize build my-app/k8s/overlays/production

# Validate generated manifests
kustomize build my-app/k8s/overlays/production | kubectl apply --dry-run=client -f -
```

### 4. Applying to Cluster

```bash
# Apply to development cluster
kustomize build my-app/k8s/overlays/dev | kubectl apply -f -

# Apply to staging cluster
kustomize build my-app/k8s/overlays/staging | kubectl apply -f -

# Apply to production cluster
kustomize build my-app/k8s/overlays/production | kubectl apply -f -
```

## Integration with ArgoCD

The Kustomize integration generates ArgoCD applications that point to the Kustomize overlay paths:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: my-app-production
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps.git
    targetRevision: main
    path: my-app/k8s/overlays/production
  destination:
    server: https://production-cluster.k8s.ondigitalocean.com
    namespace: my-app-production
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

## Multi-Cluster Support

The Kustomize system maintains full compatibility with the multi-cluster setup:

- **Development/Staging**: Target cluster (6be4e15d-52f9-431d-84ec-ec8cad0dff2d)
- **Production**: Production cluster (e9d23ae8-213c-4746-b379-330f85c0a0cf)
- **ArgoCD Management**: Management cluster (158b6a47-3e7e-4dca-af0f-05a6e07115af)

## Application Type Support

The Kustomize system supports all application types with type-specific configurations:

### React Frontend
- Port 3000 configuration
- Static file serving optimizations
- Build-time environment variables
- Health check path: `/health`

### Spring Boot Backend
- Port 8080 configuration
- Actuator health endpoints
- JVM tuning parameters
- Health check path: `/actuator/health`

### Django Backend
- Port 8000 configuration
- Django-specific settings
- Database migration support
- Health check path: `/health/`

### NestJS Backend
- Port 3000 configuration
- TypeScript optimizations
- Dependency injection support
- Health check path: `/health`

## Validation and Testing

### Pre-deployment Validation

```bash
# Validate Kustomize configuration
kustomize build my-app/k8s/overlays/production > /tmp/manifests.yaml
kubectl apply --dry-run=client -f /tmp/manifests.yaml

# Check resource quotas
kubectl describe resourcequota -n my-app-production

# Validate security contexts
kubectl auth can-i --list --as=system:serviceaccount:my-app-production:default
```

### Testing Deployment

```bash
# Test development deployment
kustomize build my-app/k8s/overlays/dev | kubectl apply -f -
kubectl rollout status deployment/my-app -n my-app-dev

# Test staging deployment
kustomize build my-app/k8s/overlays/staging | kubectl apply -f -
kubectl rollout status deployment/my-app -n my-app-staging

# Test production deployment
kustomize build my-app/k8s/overlays/production | kubectl apply -f -
kubectl rollout status deployment/my-app -n my-app-production
```

## Migration from Template-based System

### Step-by-Step Migration

1. **Generate Kustomize overlay** for existing application:
   ```bash
   python scripts/generate-manifests-cicd.py \
     --use-kustomize \
     --project-id existing-app \
     --environment production \
     # ... other parameters
   ```

2. **Compare generated manifests**:
   ```bash
   # Compare template-based vs Kustomize-based
   diff -u existing-app/k8s/ <(kustomize build existing-app/k8s/overlays/production)
   ```

3. **Update ArgoCD application**:
   ```bash
   # Update source path to Kustomize overlay
   kubectl patch application existing-app-production -n argocd --type='merge' -p='{"spec":{"source":{"path":"existing-app/k8s/overlays/production"}}}'
   ```

4. **Verify deployment**:
   ```bash
   # Check ArgoCD sync status
   argocd app get existing-app-production
   
   # Verify application health
   kubectl get pods -n existing-app-production
   ```

5. **Clean up old manifests** (after successful migration):
   ```bash
   # Remove template-based manifests
   rm -rf existing-app/k8s/
   ```

## Troubleshooting

### Common Issues

1. **Kustomize build failures**:
   - Check YAML syntax in patch files
   - Verify resource names match between base and patches
   - Ensure all referenced files exist

2. **ArgoCD sync issues**:
   - Verify repository permissions
   - Check source path in ArgoCD application
   - Validate cluster connectivity

3. **Resource conflicts**:
   - Check resource quotas and limits
   - Verify namespace isolation
   - Review security contexts

### Debug Commands

```bash
# Debug Kustomize build
kustomize build --enable-alpha-plugins my-app/k8s/overlays/production

# Check ArgoCD application status
argocd app get my-app-production --show-params

# Validate cluster resources
kubectl describe nodes
kubectl get resourcequota --all-namespaces
```

## Best Practices

1. **Use descriptive patch names** that clearly indicate their purpose
2. **Keep patches minimal** and focused on specific changes
3. **Validate configurations** before committing to repository
4. **Test in development** before promoting to staging/production
5. **Monitor resource usage** and adjust quotas as needed
6. **Document custom patches** for team understanding
7. **Use version control** for all configuration changes

The Kustomize integration provides a robust, scalable, and maintainable approach to GitOps deployments while preserving all the benefits of the dynamic multi-stage rolling update system.
