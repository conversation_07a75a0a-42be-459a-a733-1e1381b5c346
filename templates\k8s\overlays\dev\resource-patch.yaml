apiVersion: v1
kind: ResourceQuota
metadata:
  name: APP_NAME-resource-quota
spec:
  hard:
    # Development resource quotas (relaxed)
    requests.cpu: "2000m"
    limits.cpu: "4000m"
    requests.memory: "4Gi"
    limits.memory: "8Gi"
    requests.storage: "50Gi"
    pods: "10"
    services: "5"
    secrets: "10"
    configmaps: "10"
    persistentvolumeclaims: "5"
    services.loadbalancers: "2"
    services.nodeports: "3"
