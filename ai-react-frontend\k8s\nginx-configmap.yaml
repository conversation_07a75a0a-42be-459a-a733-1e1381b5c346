apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-nginx-config
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: nginx-config
    environment: dev
data:
  default.conf: |
    server {
        listen 3000;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # Serve env-config.js with no caching
        location /env-config.js {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            add_header Access-Control-Allow-Origin "*";
        }

        # Static assets with caching
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # React app - handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
