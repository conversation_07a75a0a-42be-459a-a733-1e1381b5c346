apiVersion: v1
kind: ResourceQuota
metadata:
  name: APP_NAME-resource-quota
  namespace: NAMESPACE
  labels:
    app: APP_NAME
    app.kubernetes.io/name: APP_NAME
    app.kubernetes.io/component: resource-management
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: APP_NAME
    environment: ENVIRONMENT
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  hard:
    # Compute Resource Quotas
    requests.cpu: "RESOURCE_QUOTA_CPU_REQUESTS"
    limits.cpu: "RESOURCE_QUOTA_CPU_LIMITS"
    requests.memory: "RESOURCE_QUOTA_MEMORY_REQUESTS"
    limits.memory: "RESOURCE_QUOTA_MEMORY_LIMITS"
    
    # Storage Resource Quotas
    requests.storage: "RESOURCE_QUOTA_STORAGE_REQUESTS"
    
    # Object Count Quotas
    pods: "RESOURCE_QUOTA_PODS"
    services: "RESOURCE_QUOTA_SERVICES"
    secrets: "RESOURCE_QUOTA_SECRETS"
    configmaps: "RESOURCE_QUOTA_CONFIGMAPS"
    persistentvolumeclaims: "RESOURCE_QUOTA_PVC"
    services.loadbalancers: "RESOURCE_QUOTA_LOAD_BALANCERS"
    services.nodeports: "RESOURCE_QUOTA_NODE_PORTS"
