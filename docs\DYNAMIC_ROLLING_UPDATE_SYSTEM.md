# Dynamic Multi-Stage Rolling Update Deployment System

## Overview

This document defines the comprehensive environment-aware rolling update deployment system for GitOps ArgoCD automation. The system provides industry-standard deployment strategies with complete configurability and zero hardcoded values.

## Environment-Specific Rolling Update Strategies

### Development Environment Configuration

```yaml
environment: dev
rolling_update:
  max_unavailable: "50%"
  max_surge: "50%"
  progress_deadline_seconds: 120
  revision_history_limit: 3

health_checks:
  startup_probe:
    enabled: false
  readiness_probe:
    initial_delay_seconds: 5
    period_seconds: 3
    timeout_seconds: 2
    failure_threshold: 3
    success_threshold: 1
  liveness_probe:
    initial_delay_seconds: 15
    period_seconds: 10
    timeout_seconds: 5
    failure_threshold: 3

replicas:
  min: 2
  max: 4
  default: 2

resources:
  requests:
    cpu: "200m"
    memory: "256Mi"
  limits:
    cpu: "500m"
    memory: "512Mi"

security:
  run_as_non_root: false
  read_only_root_filesystem: false
  allow_privilege_escalation: true
```

### Staging Environment Configuration

```yaml
environment: staging
rolling_update:
  max_unavailable: "25%"
  max_surge: "25%"
  progress_deadline_seconds: 300
  revision_history_limit: 5

health_checks:
  startup_probe:
    initial_delay_seconds: 10
    period_seconds: 5
    timeout_seconds: 3
    failure_threshold: 5
    success_threshold: 1
  readiness_probe:
    initial_delay_seconds: 10
    period_seconds: 5
    timeout_seconds: 3
    failure_threshold: 3
    success_threshold: 1
  liveness_probe:
    initial_delay_seconds: 30
    period_seconds: 10
    timeout_seconds: 5
    failure_threshold: 3

replicas:
  min: 3
  max: 6
  default: 3

resources:
  requests:
    cpu: "300m"
    memory: "512Mi"
  limits:
    cpu: "800m"
    memory: "1Gi"

security:
  run_as_non_root: true
  run_as_user: 1000
  read_only_root_filesystem: true
  allow_privilege_escalation: false
```

### Production Environment Configuration

```yaml
environment: production
rolling_update:
  max_unavailable: "0%"
  max_surge: "33%"
  progress_deadline_seconds: 600
  revision_history_limit: 10

health_checks:
  startup_probe:
    initial_delay_seconds: 30
    period_seconds: 10
    timeout_seconds: 5
    failure_threshold: 10
    success_threshold: 1
  readiness_probe:
    initial_delay_seconds: 15
    period_seconds: 10
    timeout_seconds: 5
    failure_threshold: 3
    success_threshold: 1
  liveness_probe:
    initial_delay_seconds: 60
    period_seconds: 30
    timeout_seconds: 10
    failure_threshold: 3

replicas:
  min: 3
  max: 10
  default: 3

resources:
  requests:
    cpu: "500m"
    memory: "1Gi"
  limits:
    cpu: "1000m"
    memory: "2Gi"

security:
  run_as_non_root: true
  run_as_user: 1000
  read_only_root_filesystem: true
  allow_privilege_escalation: false
  capabilities:
    drop: ["ALL"]
```

## Application-Type-Specific Health Check Paths

### React Frontend Applications
- **Health Check Path**: `/health` (custom endpoint)
- **Fallback Path**: `/` (root path)
- **Protocol**: HTTP
- **Port**: Dynamic based on CONTAINER_PORT (3000 or 80)

### Spring Boot Backend Applications
- **Health Check Path**: `/actuator/health`
- **Readiness Path**: `/actuator/health/readiness`
- **Liveness Path**: `/actuator/health/liveness`
- **Protocol**: HTTP
- **Port**: Dynamic based on CONTAINER_PORT (default 8080)

### Django Backend Applications
- **Health Check Path**: `/health/`
- **Readiness Path**: `/health/ready/`
- **Liveness Path**: `/health/live/`
- **Protocol**: HTTP
- **Port**: Dynamic based on CONTAINER_PORT (default 8000)

### NestJS Backend Applications
- **Health Check Path**: `/health`
- **Readiness Path**: `/health/readiness`
- **Liveness Path**: `/health/liveness`
- **Protocol**: HTTP
- **Port**: Dynamic based on CONTAINER_PORT (default 3000)

### Generic Applications
- **Health Check Path**: `/health`
- **Protocol**: HTTP
- **Port**: Dynamic based on CONTAINER_PORT

## Template Variables for Rolling Updates

### Core Rolling Update Variables
- `{{ROLLING_UPDATE_MAX_UNAVAILABLE}}` - Maximum unavailable pods during update
- `{{ROLLING_UPDATE_MAX_SURGE}}` - Maximum surge pods during update
- `{{ROLLING_UPDATE_PROGRESS_DEADLINE}}` - Progress deadline in seconds
- `{{ROLLING_UPDATE_REVISION_HISTORY}}` - Number of old ReplicaSets to retain

### Health Check Variables
- `{{STARTUP_PROBE_ENABLED}}` - Enable startup probe (boolean)
- `{{STARTUP_PROBE_INITIAL_DELAY}}` - Startup probe initial delay
- `{{STARTUP_PROBE_PERIOD}}` - Startup probe period
- `{{STARTUP_PROBE_TIMEOUT}}` - Startup probe timeout
- `{{STARTUP_PROBE_FAILURE_THRESHOLD}}` - Startup probe failure threshold

- `{{READINESS_PROBE_INITIAL_DELAY}}` - Readiness probe initial delay
- `{{READINESS_PROBE_PERIOD}}` - Readiness probe period
- `{{READINESS_PROBE_TIMEOUT}}` - Readiness probe timeout
- `{{READINESS_PROBE_FAILURE_THRESHOLD}}` - Readiness probe failure threshold

- `{{LIVENESS_PROBE_INITIAL_DELAY}}` - Liveness probe initial delay
- `{{LIVENESS_PROBE_PERIOD}}` - Liveness probe period
- `{{LIVENESS_PROBE_TIMEOUT}}` - Liveness probe timeout
- `{{LIVENESS_PROBE_FAILURE_THRESHOLD}}` - Liveness probe failure threshold

- `{{HEALTH_CHECK_PATH}}` - HTTP health check path
- `{{HEALTH_CHECK_PORT}}` - HTTP health check port

### Security Context Variables
- `{{SECURITY_RUN_AS_NON_ROOT}}` - Run as non-root user (boolean)
- `{{SECURITY_RUN_AS_USER}}` - User ID to run as
- `{{SECURITY_READ_ONLY_ROOT_FS}}` - Read-only root filesystem (boolean)
- `{{SECURITY_ALLOW_PRIVILEGE_ESCALATION}}` - Allow privilege escalation (boolean)
- `{{SECURITY_CAPABILITIES_DROP}}` - Capabilities to drop (comma-separated)

### Resource Management Variables
- `{{REPLICAS_MIN}}` - Minimum replica count
- `{{REPLICAS_MAX}}` - Maximum replica count for HPA
- `{{REPLICAS_DEFAULT}}` - Default replica count

### Pod Disruption Budget Variables
- `{{PDB_MIN_AVAILABLE}}` - Minimum available pods
- `{{PDB_MAX_UNAVAILABLE}}` - Maximum unavailable pods

### HPA Variables
- `{{HPA_ENABLED}}` - Enable Horizontal Pod Autoscaler (boolean)
- `{{HPA_MIN_REPLICAS}}` - HPA minimum replicas
- `{{HPA_MAX_REPLICAS}}` - HPA maximum replicas
- `{{HPA_TARGET_CPU_UTILIZATION}}` - Target CPU utilization percentage
- `{{HPA_TARGET_MEMORY_UTILIZATION}}` - Target memory utilization percentage

## Cluster Resource Optimization

### Resource Allocation Strategy
Based on cluster constraints (4 vCPU/8GB RAM per cluster):

#### Development Cluster Allocation
- **Total Capacity**: 4 vCPU, 8GB RAM
- **System Reserved**: 0.5 vCPU, 1GB RAM
- **Available for Apps**: 3.5 vCPU, 7GB RAM
- **Max Apps (default resources)**: ~7 applications

#### Staging/Production Cluster Allocation
- **Total Capacity**: 4 vCPU, 8GB RAM
- **System Reserved**: 0.5 vCPU, 1GB RAM
- **Available for Apps**: 3.5 vCPU, 7GB RAM
- **Max Apps (default resources)**: ~4-5 applications

### Resource Quota Templates
- `{{RESOURCE_QUOTA_CPU_REQUESTS}}` - Total CPU requests limit
- `{{RESOURCE_QUOTA_CPU_LIMITS}}` - Total CPU limits
- `{{RESOURCE_QUOTA_MEMORY_REQUESTS}}` - Total memory requests limit
- `{{RESOURCE_QUOTA_MEMORY_LIMITS}}` - Total memory limits
- `{{RESOURCE_QUOTA_PODS}}` - Maximum number of pods

## Implementation Status

### ✅ Phase 1: Enhanced Deployment Template (COMPLETED)
1. ✅ Updated `templates/k8s/deployment.yaml` with complete parameterization
2. ✅ Added environment-conditional rolling update strategies
3. ✅ Implemented universal HTTP health checks
4. ✅ Added security contexts and resource optimization
5. ✅ Enhanced service template with comprehensive configuration

### ✅ Phase 2: Additional Resource Templates (COMPLETED)
1. ✅ Created `templates/k8s/pdb.yaml` for Pod Disruption Budgets
2. ✅ Created `templates/k8s/hpa.yaml` for Horizontal Pod Autoscaler
3. ✅ Created `templates/k8s/resource-quota.yaml` for namespace resource limits
4. ✅ Created `templates/k8s/limit-range.yaml` for default resource constraints
5. ✅ Created `templates/k8s/network-policy.yaml` for security isolation

### ✅ Phase 3: Processing Script Updates (COMPLETED)
1. ✅ Created `scripts/dynamic-environment-config.py` with environment-specific logic
2. ✅ Updated Python script with new variable handling and dynamic configuration
3. ✅ Added validation for resource constraints and cluster capacity
4. ✅ Implemented application-type-aware configurations
5. ✅ Maintained backward compatibility with legacy configurations

### ✅ Phase 4: Documentation and Examples (COMPLETED)
1. ✅ Created comprehensive sample payloads for all application types
2. ✅ Documented all template variables with environment-specific defaults
3. ✅ Provided React frontend sample payload (no database)
4. ✅ Created implementation guides and best practices
5. ✅ Ensured complete reusability across any project type

## Key Features Implemented

### 🎯 Dynamic Multi-Stage Rolling Updates
- Environment-aware rolling update strategies (dev: 50%/50%, staging: 25%/25%, prod: 0%/33%)
- Configurable progress deadlines (dev: 120s, staging: 300s, prod: 600s)
- Revision history management (dev: 3, staging: 5, prod: 10)

### 🏥 Universal HTTP Health Checks
- Application-type-specific health check paths
- Environment-conditional probe timings
- Startup, readiness, and liveness probes
- Configurable timeouts and failure thresholds

### 🔒 Enhanced Security
- Environment-specific security contexts
- Non-root user execution in staging/production
- Read-only root filesystems
- Capability dropping in production
- Network policies for traffic isolation

### 📊 Resource Optimization
- Cluster-capacity-aware resource allocation
- Environment-specific resource profiles
- Automatic resource quotas and limit ranges
- Horizontal Pod Autoscaler with intelligent scaling

### 🚀 Zero Hardcoded Values
- Complete template parameterization
- Dynamic configuration based on environment and application type
- Configurable through CI/CD payloads or GitHub issues
- Backward compatibility with existing deployments

## Usage Instructions

### 1. Deploy React Frontend (No Database)
```bash
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "My React Dashboard",
      "project_id": "my-react-dashboard",
      "application_type": "react-frontend",
      "environment": "dev",
      "docker_image": "myorg/react-dashboard",
      "docker_tag": "v1.2.0",
      "container_port": 3000,
      "source_repo": "myorg/react-dashboard",
      "source_branch": "main",
      "commit_sha": "abc123def456",
      "enable_database": false,
      "health_check_path": "/health",
      "secrets_encoded": "base64-encoded-secrets"
    }
  }'
```

### 2. Test Locally
```bash
python scripts/generate-manifests-cicd.py \
  --app-name "Test React App" \
  --project-id "test-react-app" \
  --application-type "react-frontend" \
  --environment "dev" \
  --docker-image "nginx" \
  --docker-tag "alpine" \
  --container-port 3000 \
  --source-repo "test/react-app" \
  --source-branch "main" \
  --commit-sha "test123" \
  --output-dir "."
```

### 3. Generated Resources
The system automatically generates:
- **Core**: Namespace, Deployment, Service, ConfigMap, Secret
- **Enhanced**: PDB, HPA, ResourceQuota, LimitRange, NetworkPolicy
- **ArgoCD**: Application, AppProject
- **Database**: PostgreSQL resources (if enabled)

## Benefits Achieved

### 🎯 Industry-Standard Deployments
- Zero-downtime rolling updates in production
- Proper resource management and scaling
- Security best practices enforcement
- Comprehensive monitoring and observability

### 🔧 Complete Automation
- No manual configuration required
- Environment-aware defaults
- Application-type optimizations
- Cluster-capacity optimization

### 🛡️ Enhanced Security
- Non-root containers in production
- Network traffic isolation
- Resource quota enforcement
- Security context validation

### 📈 Scalability and Reliability
- Horizontal Pod Autoscaling
- Pod Disruption Budgets
- Resource optimization
- Health check validation

The dynamic multi-stage rolling update deployment system is now fully implemented and ready for production use across any application type and deployment scenario.
