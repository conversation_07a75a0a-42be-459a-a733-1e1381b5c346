### Application Name

AI Frontend React Test

### Project Identifier

ai-frontend-react

### Container Image

registry.digitalocean.com/doks-registry/ai-react-frontend:latest

### Environment

dev

### Target Cluster

dev-staging (6be4e15d-52f9-431d-84ec-ec8cad0dff2d)

### Kubernetes Namespace

ai-frontend-react

### Container Port

3000

### Application Type

react-frontend

### Health Check Path

/health

### Backend Type

django

### Runtime Configuration

- [x] Enable runtime configuration (allows switching backends without rebuilding)

### Additional Notes

Testing the new approach with runtime configuration enabled and Django backend type.
