#!/bin/bash

# Script to create DigitalOcean Container Registry authentication secret
# This script creates image pull secrets for accessing private DigitalOcean container registry

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v doctl &> /dev/null; then
        print_error "doctl is not installed or not in PATH"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Check if DigitalOcean access token is available
check_do_auth() {
    print_status "Checking DigitalOcean authentication..."
    
    if [ -z "$DIGITALOCEAN_ACCESS_TOKEN" ]; then
        print_error "DIGITALOCEAN_ACCESS_TOKEN environment variable is not set"
        print_error "Please set it with: export DIGITALOCEAN_ACCESS_TOKEN='your-token-here'"
        exit 1
    fi
    
    # Test doctl authentication
    if ! doctl auth init --access-token "$DIGITALOCEAN_ACCESS_TOKEN" &> /dev/null; then
        print_error "Failed to authenticate with DigitalOcean"
        print_error "Please check your DIGITALOCEAN_ACCESS_TOKEN"
        exit 1
    fi
    
    print_success "DigitalOcean authentication successful"
}

# Get registry credentials from DigitalOcean
get_registry_credentials() {
    print_status "Getting DigitalOcean container registry credentials..."
    
    # Get registry login token
    REGISTRY_TOKEN=$(doctl registry docker-config --expiry-seconds 3600 | jq -r '.auths."registry.digitalocean.com".auth')
    
    if [ -z "$REGISTRY_TOKEN" ] || [ "$REGISTRY_TOKEN" = "null" ]; then
        print_error "Failed to get registry token from DigitalOcean"
        exit 1
    fi
    
    print_success "Registry credentials obtained"
}

# Create image pull secret in specified namespace
create_image_pull_secret() {
    local namespace=$1
    local secret_name="digitalocean-registry"
    
    print_status "Creating image pull secret in namespace: $namespace"
    
    # Create namespace if it doesn't exist
    if ! kubectl get namespace "$namespace" &> /dev/null; then
        print_status "Creating namespace: $namespace"
        kubectl create namespace "$namespace"
    fi
    
    # Delete existing secret if it exists
    if kubectl get secret "$secret_name" -n "$namespace" &> /dev/null; then
        print_warning "Deleting existing secret: $secret_name"
        kubectl delete secret "$secret_name" -n "$namespace"
    fi
    
    # Create the image pull secret
    kubectl create secret docker-registry "$secret_name" \
        --namespace="$namespace" \
        --docker-server=registry.digitalocean.com \
        --docker-username=token \
        --docker-password="$(echo $REGISTRY_TOKEN | base64 -d)" \
        --docker-email=unused
    
    if [ $? -eq 0 ]; then
        print_success "Image pull secret created: $secret_name in namespace $namespace"
    else
        print_error "Failed to create image pull secret in namespace $namespace"
        exit 1
    fi
}

# Main function
main() {
    print_status "Starting DigitalOcean Container Registry secret creation..."
    
    # Check prerequisites
    check_prerequisites
    
    # Check DigitalOcean authentication
    check_do_auth
    
    # Get registry credentials
    get_registry_credentials
    
    # Define namespaces that need registry access
    NAMESPACES=(
        "ai-spring-backend-dev"
        "ai-spring-backend-staging"
        "ai-spring-backend-production"
        "ai-nest-backend-dev"
        "ai-nest-backend-staging"
        "ai-nest-backend-production"
        "ai-react-frontend-dev"
        "ai-react-frontend-staging"
        "ai-react-frontend-production"
        "default"
    )
    
    # Create image pull secrets in all required namespaces
    for namespace in "${NAMESPACES[@]}"; do
        create_image_pull_secret "$namespace"
    done
    
    print_success "All image pull secrets created successfully!"
    print_status "You can now deploy applications that use DigitalOcean container registry"
    
    # Show created secrets
    print_status "Created secrets:"
    for namespace in "${NAMESPACES[@]}"; do
        if kubectl get namespace "$namespace" &> /dev/null; then
            echo "  - digitalocean-registry in $namespace"
        fi
    done
}

# Run main function
main "$@"
